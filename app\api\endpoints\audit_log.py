from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from sqlalchemy.orm import Session

from app import crud, models
from app.api import deps
from app.schemas.audit_log import AuditLog, AuditLogFilter, AuditLogResponse

router = APIRouter()


@router.get("/", response_model=AuditLogResponse)
def get_audit_logs(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    action_type: Optional[str] = Query(None, description="Filter by action type"),
    record_type: Optional[str] = Query(None, description="Filter by record type"),
    record_id: Optional[str] = Query(None, description="Filter by record ID"),
    edit_by: Optional[str] = Query(None, description="Filter by user ID"),
    batch_id: Optional[str] = Query(None, description="Filter by batch ID"),
    start_date: Optional[str] = Query(None, description="Start date (ISO format)"),
    end_date: Optional[str] = Query(None, description="End date (ISO format)"),
    search: Optional[str] = Query(
        None, description="Search in record name and details"
    ),
) -> Any:
    """
    Get paginated audit logs with optional filters.
    """
    from datetime import datetime

    # Parse dates
    start_dt = None
    end_dt = None
    if start_date:
        try:
            start_dt = datetime.fromisoformat(start_date.replace("Z", "+00:00"))
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid start_date format")

    if end_date:
        try:
            end_dt = datetime.fromisoformat(end_date.replace("Z", "+00:00"))
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid end_date format")

    filters = AuditLogFilter(
        action_type=action_type,
        record_type=record_type,
        record_id=record_id,
        edit_by=edit_by,
        batch_id=batch_id,
        start_date=start_dt,
        end_date=end_dt,
        search=search,
    )

    result = crud.audit_log.get_paginated(
        db=db,
        filters=filters,
        skip=skip,
        limit=limit,
        organization_id=str(current_user.organization_id),
    )

    return AuditLogResponse(**result)


@router.get("/record/{record_type}/{record_id}", response_model=List[AuditLog])
def get_record_history(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    record_type: str,
    record_id: str,
    limit: int = Query(50, ge=1, le=200, description="Number of records to return"),
) -> Any:
    """
    Get audit history for a specific record.
    """
    history = crud.audit_log.get_record_history(
        db=db,
        record_id=record_id,
        record_type=record_type,
        organization_id=str(current_user.organization_id),
        limit=limit,
    )

    return history


@router.get("/user/{user_id}/activity", response_model=List[AuditLog])
def get_user_activity(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    user_id: str,
    days: int = Query(30, ge=1, le=365, description="Number of days to look back"),
    limit: int = Query(100, ge=1, le=500, description="Number of records to return"),
) -> Any:
    """
    Get recent activity for a specific user.
    """
    activity = crud.audit_log.get_user_activity(
        db=db,
        user_id=user_id,
        organization_id=str(current_user.organization_id),
        days=days,
        limit=limit,
    )

    return activity


@router.get("/my-activity", response_model=List[AuditLog])
def get_my_activity(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    days: int = Query(30, ge=1, le=365, description="Number of days to look back"),
    limit: int = Query(100, ge=1, le=500, description="Number of records to return"),
) -> Any:
    """
    Get recent activity for the current user.
    """
    activity = crud.audit_log.get_user_activity(
        db=db,
        user_id=str(current_user.id),
        organization_id=str(current_user.organization_id),
        days=days,
        limit=limit,
    )

    return activity


@router.get("/statistics")
def get_audit_statistics(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
) -> Any:
    """
    Get audit log statistics for the organization.
    """
    stats = crud.audit_log.get_statistics(
        db=db, organization_id=str(current_user.organization_id), days=days
    )

    return stats


@router.get("/action-types")
def get_action_types() -> List[str]:
    """
    Get list of available action types.
    """
    return ["create", "update", "delete", "proposed", "gave_feedback"]


@router.get("/record-types")
def get_record_types() -> List[str]:
    """
    Get list of available record types.
    """
    return [
        "player",
        "activity",
        "team_request",
        "shadow_squad",
        "shadow_request",
        "contract",
        "organization",
        "user",
        "contact",
        "proposal",
        "report",
        "community_proposal",
        "community_deal",
        "staff_record",
        "priority_player",
        "favourite_team",
    ]
