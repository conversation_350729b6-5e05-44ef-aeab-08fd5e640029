"""Priority player is back

Revision ID: 34578446c448
Revises: 07b8befcc797
Create Date: 2025-08-11 11:13:08.635039

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '34578446c448'
down_revision = '07b8befcc797'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('player_records', sa.Column('is_priority', sa.<PERSON>(), nullable=True), schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('player_records', 'is_priority', schema='crm_test')
    # ### end Alembic commands ###