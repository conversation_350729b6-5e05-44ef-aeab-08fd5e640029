import uuid
from sqlalchemy import Column, String, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.config import settings


class OrganizationTag(Base):
    __tablename__ = "organization_tags"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(
        UUID(as_uuid=True), 
        ForeignKey(f"{settings.PG_SCHEMA}.organizations.id"), 
        index=True,
        nullable=False
    )
    organization = relationship("Organization")
    type_of_tags = Column(String, nullable=False)  # "players" or "teams"
    tags = Column(ARRAY(String), nullable=False, default=[])
