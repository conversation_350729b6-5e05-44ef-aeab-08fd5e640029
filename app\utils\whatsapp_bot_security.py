import secrets
import hmac
import hashlib
from typing import Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Depends, Request
from sqlalchemy.orm import Session
from app.config import settings
from app.models.user import User
from sqlalchemy import func


def verify_api_key(x_api_key: Optional[str] = Header(None)) -> bool:
    """
    Verify the API key from the X-API-Key header.
    This should be used for WhatsApp bot endpoints.
    """
    if not x_api_key:
        raise HTTPException(
            status_code=401, detail="Missing API key. Include X-API-Key header."
        )

    # Use constant-time comparison to prevent timing attacks
    if not secrets.compare_digest(x_api_key, settings.WHATSAPP_BOT_API_KEY):
        raise HTTPException(status_code=401, detail="Invalid API key")

    return True


def get_user_by_phone(phone_number: str, db: Session) -> User:
    """
    Get user by phone number with WhatsApp access enabled.
    """
    user = (
        db.query(User)
        .filter(
            func.regexp_replace(User.phone_number, r"\s+", "", "g") == phone_number,
            User.use_whatsapp == True,
        )
        .first()
    )

    if not user:
        raise HTTPException(
            status_code=403,
            detail="Unauthorized: This phone number is not registered for WhatsApp use",
        )

    return user


async def validate_whatsapp_bot_request(
    phone_number: str, db: Session, api_key_valid: bool = Depends(verify_api_key)
) -> User:
    """
    Complete validation for WhatsApp bot requests:
    1. Verify API key
    2. Validate phone number and get user

    This is the main dependency to use in WhatsApp bot endpoints.
    """
    # Format phone number consistently - handle URL decoding and ensure single + prefix
    formatted_phone = phone_number.strip()
    if not formatted_phone.startswith("+"):
        formatted_phone = f"+{formatted_phone}"

    # Get and validate user
    user = get_user_by_phone(formatted_phone, db)

    return user


# Rate limiting decorator (optional enhancement)
class RateLimiter:
    """
    Simple in-memory rate limiter for WhatsApp bot endpoints.
    In production, consider using Redis for distributed rate limiting.
    """

    def __init__(self, max_requests: int = 10, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}  # phone_number -> list of timestamps

    def is_allowed(self, phone_number: str) -> bool:
        import time

        now = time.time()

        # Clean old requests
        if phone_number in self.requests:
            self.requests[phone_number] = [
                req_time
                for req_time in self.requests[phone_number]
                if now - req_time < self.window_seconds
            ]
        else:
            self.requests[phone_number] = []

        # Check if under limit
        if len(self.requests[phone_number]) >= self.max_requests:
            return False

        # Add current request
        self.requests[phone_number].append(now)
        return True


# Global rate limiter instance
whatsapp_rate_limiter = RateLimiter(max_requests=20, window_seconds=60)


def check_rate_limit(phone_number: str) -> bool:
    """
    Check if the phone number is within rate limits.
    """
    if not whatsapp_rate_limiter.is_allowed(phone_number):
        raise HTTPException(
            status_code=429,
            detail="Rate limit exceeded. Too many requests from this phone number.",
        )
    return True
