from typing import List, Optional
import uuid
from pydantic import BaseModel, validator
from app.schemas.enums import TagType


class OrganizationTagCreate(BaseModel):
    type_of_tags: TagType
    tags: List[str] = []
    
    @validator('tags')
    def validate_tags(cls, v):
        if not isinstance(v, list):
            raise ValueError('Tags must be a list')
        # Remove empty strings and duplicates while preserving order
        cleaned_tags = []
        seen = set()
        for tag in v:
            if isinstance(tag, str) and tag.strip() and tag.strip() not in seen:
                cleaned_tag = tag.strip()
                cleaned_tags.append(cleaned_tag)
                seen.add(cleaned_tag)
        return cleaned_tags


class OrganizationTagUpdate(BaseModel):
    tags: List[str]
    
    @validator('tags')
    def validate_tags(cls, v):
        if not isinstance(v, list):
            raise ValueError('Tags must be a list')
        # Remove empty strings and duplicates while preserving order
        cleaned_tags = []
        seen = set()
        for tag in v:
            if isinstance(tag, str) and tag.strip() and tag.strip() not in seen:
                cleaned_tag = tag.strip()
                cleaned_tags.append(cleaned_tag)
                seen.add(cleaned_tag)
        return cleaned_tags


class OrganizationTag(OrganizationTagCreate):
    id: uuid.UUID
    organization_id: uuid.UUID
    
    class Config:
        orm_mode = True


class OrganizationTagResponse(BaseModel):
    type_of_tags: TagType
    tags: List[str]
    
    class Config:
        orm_mode = True
