from typing import Any, List

from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session

from app.schemas.comment import Comment, CommentUpdate, CommentCreate
from app import crud, models
from app.api import deps, utils
from app.utils.audit_decorator import audit_action

router = APIRouter()


@router.post("/", response_model=Comment)
@audit_action(action_type="create", record_type="comment")
def create_comment(
    *,
    db: Session = Depends(deps.get_db),
    comment_in: CommentCreate,
    request: Request,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new comment.
    """
    utils.check_create(CommentCreate, current_user)
    comment = crud.comment.create(
        db=db,
        obj_in=comment_in,
    )
    return comment

@router.put("/{id}", response_model=Comment)
@audit_action(
    action_type="update",
    record_type="comment",
    get_original_object=lambda kwargs: crud.comment.get(
        kwargs['db'], kwargs['id']
    )
)
def update_comment(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    request: Request,
    comment_in: CommentUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a comment.
    """
    comment = crud.comment.get(db=db, id=id)
    utils.check_modify(comment, current_user)
    comment = crud.comment.update(db=db, db_obj=comment, obj_in=comment_in)
    return comment

@router.delete("/{id}", response_model=Comment)
@audit_action(action_type="delete", record_type="comment",)
def delete_comment(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    request: Request,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a comment.
    """
    comment = crud.comment.get(db=db, id=id)
    utils.check_delete(comment, current_user)
    comment_out = Comment.from_orm(comment)
    crud.comment.remove(db=db, id=id)
    return comment_out

@router.get("/player/{player_id}", response_model=List[Comment])
def read_comments_for_a_player(
    *,
    db: Session = Depends(deps.get_db),
    player_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get All comments by player_id.
    """
    utils.check_get_all(Comment, current_user)
    comments = crud.comment.get_all_filtered_for_player(db=db, id=player_id)
    return comments

@router.get("/staff/{staff_id}", response_model=List[Comment])
def read_comments_for_a_staff(
    *,
    db: Session = Depends(deps.get_db),
    staff_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get All comments by staff_id.
    """
    utils.check_get_all(Comment, current_user)
    comments = crud.comment.get_all_filtered_for_staff(db=db, id=staff_id)
    return comments

