from typing import Any

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.schemas.organization import OrganizationCreate
from app import crud, models
from app.api import deps, utils
from app.config import settings
from sqlalchemy import text
from app.utils.admin_logging import log_admin_action

router = APIRouter()


@router.get("/")
async def read_all_organizations(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve all Organizations with their module information and record counts.
    """
    utils.check_access_admin(current_user)

    query = text(
        f"""SELECT o.id,
            o.name,
            ct.tokens,
            mods.modules,
            pr.cnt  AS player_records_count,
            tr.cnt  AS team_requests_count,
            act.cnt AS activity_tracker_count
        FROM {settings.PG_SCHEMA}.organizations o
        LEFT JOIN {settings.PG_SCHEMA}.community_tokens ct ON ct.organization_id = o.id
        LEFT JOIN (
            SELECT p.organization_id,
                ARRAY_AGG(DISTINCT m.name) AS modules
            FROM {settings.PG_SCHEMA}.purchases p
            JOIN {settings.PG_SCHEMA}.modules m ON m.id = p.module_id
            GROUP BY p.organization_id
        ) mods ON mods.organization_id = o.id
        LEFT JOIN (
            SELECT organization_id, COUNT(*) AS cnt
            FROM {settings.PG_SCHEMA}.player_records
            GROUP BY organization_id
        ) pr  ON pr.organization_id  = o.id
        LEFT JOIN (
            SELECT organization_id, COUNT(*) AS cnt
            FROM {settings.PG_SCHEMA}.team_requests
            GROUP BY organization_id
        ) tr  ON tr.organization_id = o.id
        LEFT JOIN (
            SELECT organization_id, COUNT(*) AS cnt
            FROM {settings.PG_SCHEMA}.activity
            GROUP BY organization_id
        ) act ON act.organization_id = o.id;"""
    )
    organizations = db.execute(query).mappings().all()

    return organizations


@router.post("/", response_model=Any)
async def create_organization(
    *,
    db: Session = Depends(deps.get_db),
    obj_in: OrganizationCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new Organization.
    """
    utils.check_access_admin(current_user)
    organization = crud.organization.create(
        db=db,
        obj_in=obj_in,
    )

    # Log the creation
    log_admin_action(
        db=db,
        user=current_user,
        action_type="create_organization",
        target_type="organization",
        target_id=organization.id,
        details=f"Created organization: {organization.name}",
    )

    return organization


@router.get("/my_organization")
async def read_my_organizations(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve Organizations the current user belongs to.
    """

    query = text(
        f"""
        SELECT 
            o.name AS organization_name,
            o.agency_id,
            o.agency_logo_url,
            o.password as agency_status,
            m.name AS module_name,
            ur.name AS user_role,
            u.first_name,
            u.last_name,
            u.email,
            u.phone_number,
            u.is_active,
            u.is_enabled,
            u.is_superuser,
            u.is_verified,
            u.id as user_id,
            ct.tokens,
            case
                when o.id in ('af244f52-6ed3-48ee-901a-7eaedc243e31') then True
                else False
            end as has_gbp
        FROM {settings.PG_SCHEMA}.organizations o
        left JOIN {settings.PG_SCHEMA}.purchases p ON p.organization_id = o.id
        left JOIN {settings.PG_SCHEMA}.modules m ON m.id = p.module_id 
        left JOIN {settings.PG_SCHEMA}.user u ON u.id = :user_id AND u.organization_id = o.id
        JOIN {settings.PG_SCHEMA}.user_roles ur ON ur.id = u.role_id
        left JOIN {settings.PG_SCHEMA}.community_tokens ct ON ct.organization_id = o.id
        """
    )
    results = db.execute(query, {"user_id": current_user.id}).mappings().all()
    if not results:
        return {}

    # All results share same org/user info; just collect modules
    first = results[0]
    modules = sorted(
        set(r["module_name"] for r in results)
    )  # optional: remove sorted() if not needed
    return {
        "organization_name": first["organization_name"],
        "agency_id": first["agency_id"],
        "agency_logo_url": first["agency_logo_url"],
        "agency_status": first["agency_status"],
        "user_role": first["user_role"],
        "email": first["email"],
        "first_name": first["first_name"],
        "last_name": first["last_name"],
        "phone_number": first["phone_number"],
        "is_active": first["is_active"],
        "is_enabled": first["is_enabled"],
        "is_superuser": first["is_superuser"],
        "is_verified": first["is_verified"],
        "user_id": first["user_id"],
        "modules": modules,
        "tokens": first["tokens"],
        "has_gbp": first["has_gbp"],
    }


@router.delete("/{organization_id}")
async def delete_organization(
    organization_id: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete an Organization and all its dependent records.
    This will delete:
    - All users in the organization
    - All contacts
    - All player records
    - All team requests
    - All purchases
    - All community tokens
    - All football fields
    - All rank records
    """
    utils.check_access_admin(current_user)

    # First check if organization exists
    organization = crud.organization.get(db=db, id=organization_id)
    if not organization:
        raise HTTPException(status_code=404, detail="Organization not found")

    try:
        # Get all users in the organization first
        users = (
            db.query(models.User)
            .filter(models.User.organization_id == organization_id)
            .all()
        )

        # Delete user_roles_default entries for these users
        for user in users:
            db.query(models.UserDefaultRoles).filter(
                models.UserDefaultRoles.user_id == user.id
            ).delete()

        # Delete all dependent records
        # Delete community tokens
        db.query(models.CommunityTokens).filter(
            models.CommunityTokens.organization_id == organization_id
        ).delete(synchronize_session=False)

        # Delete purchases
        db.query(models.Purchase).filter(
            models.Purchase.organization_id == organization_id
        ).delete(synchronize_session=False)

        # Delete football fields
        db.query(models.FootballField).filter(
            models.FootballField.organization_id == organization_id
        ).delete(synchronize_session=False)

        # Delete rank records
        db.query(models.RankRecord).filter(
            models.RankRecord.organization_id == organization_id
        ).delete(synchronize_session=False)

        # Delete team requests
        db.query(models.TeamRequest).filter(
            models.TeamRequest.organization_id == organization_id
        ).delete(synchronize_session=False)

        # Delete player records
        db.query(models.PlayerRecord).filter(
            models.PlayerRecord.organization_id == organization_id
        ).delete(synchronize_session=False)

        # Delete contacts
        db.query(models.Contact).filter(
            models.Contact.organization_id == organization_id
        ).delete(synchronize_session=False)

        # Delete users
        db.query(models.User).filter(
            models.User.organization_id == organization_id
        ).delete(synchronize_session=False)

        # Finally delete the organization
        db.query(models.Organization).filter(
            models.Organization.id == organization_id
        ).delete(synchronize_session=False)

        db.commit()

        # Log the deletion
        log_admin_action(
            db=db,
            user=current_user,
            action_type="delete_organization",
            target_type="organization",
            target_id=organization_id,
            details=f"Deleted organization: {organization.name} and all its dependent records",
        )

        return {
            "message": "Organization and all its dependent records successfully deleted"
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Error deleting organization: {str(e)}"
        )
