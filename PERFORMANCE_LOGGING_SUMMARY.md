# Team Requests Performance Logging Implementation

## Summary

I've implemented comprehensive performance logging for the team requests endpoint to help identify and resolve performance bottlenecks. This logging system provides detailed timing information at multiple levels of the application stack.

## What Was Added

### 1. Endpoint-Level Performance Logging

**File**: `app/api/endpoints/team_requests.py`

Added detailed timing logs for the main `read_team_requests` endpoint:
- Permission checks timing
- Field exclusion setup timing
- Private requests database query timing
- Community requests database query timing
- Serialization timing (both private and community requests)
- Total request timing with breakdown

**Log Tag**: `[TEAM_REQUESTS_PERF]`

### 2. CRUD-Level Performance Logging

**File**: `app/crud/crud_team_request.py`

Added detailed timing logs for database operations:
- Query construction timing
- Filter application timing
- Query execution timing
- Result processing timing

**Log Tag**: `[CRUD_PERF]`

### 3. SQL Query Performance Logging

**File**: `app/db/session.py`

Added SQLAlchemy event listeners to log slow database queries:
- Automatically logs queries that take longer than 100ms
- Includes query preview and execution time
- Helps identify specific slow SQL statements

**Log Tag**: `[SQL_PERF]`

### 4. Performance Testing Tools

**Files**: 
- `test_team_requests_performance.py` - Script to test endpoint performance
- `TEAM_REQUESTS_PERFORMANCE_ANALYSIS.md` - Detailed analysis guide

## How to Use

### 1. Run the Performance Test

```bash
python test_team_requests_performance.py
```

This will test the endpoint with different filters and show timing information.

### 2. Monitor Application Logs

Look for these log patterns in your application logs:

```bash
# Endpoint-level performance
grep "\[TEAM_REQUESTS_PERF\]" app.log

# Database operation performance
grep "\[CRUD_PERF\]" app.log

# Slow SQL queries
grep "\[SQL_PERF\]" app.log
```

### 3. Analyze Performance Bottlenecks

The logs will show a breakdown like this:

```
[TEAM_REQUESTS_PERF] Breakdown - Permissions: 0.001s, Exclusions: 0.000s, Private Query: 0.245s, Community Query: 1.850s, Serialization: 0.120s
```

This tells you exactly where time is being spent:
- **Permissions**: 0.001s (very fast)
- **Exclusions**: 0.000s (very fast)
- **Private Query**: 0.245s (acceptable)
- **Community Query**: 1.850s (bottleneck!)
- **Serialization**: 0.120s (acceptable)

## Expected Performance Baselines

### Database Queries
- **Private requests query**: < 0.5s for typical datasets
- **Community requests query**: < 1.0s for typical datasets (this is complex)

### Serialization
- **Private requests**: < 0.1s per 100 records
- **Community requests**: < 0.2s per 100 records

### Total Response Time
- **Overall endpoint**: < 2.0s for typical datasets

## Common Issues and Solutions

### 1. Slow Community Query (Most Likely Issue)

**Symptoms**: High `[CRUD_PERF] get_community total time`

**Potential Causes**:
- Complex SQL with multiple JOINs and subqueries
- Missing database indexes
- Large dataset without proper filtering

**Solutions**:
- Review and optimize the complex SQL query in `get_community`
- Add database indexes on frequently queried columns
- Consider breaking the query into smaller parts
- Add pagination to limit result set size

### 2. Slow Serialization

**Symptoms**: High serialization times in `[TEAM_REQUESTS_PERF]` logs

**Potential Causes**:
- Large number of records being serialized
- Complex nested relationships
- Inefficient Pydantic model configuration

**Solutions**:
- Implement pagination
- Optimize Pydantic models with caching
- Reduce the number of fields being serialized
- Consider lazy loading for relationships

### 3. N+1 Query Problems

**Symptoms**: Many `[SQL_PERF]` logs showing similar queries

**Potential Causes**:
- Relationships not properly eager-loaded
- Serialization triggering additional database queries

**Solutions**:
- Review `selectinload` and `joinedload` usage in CRUD methods
- Ensure all required relationships are loaded in the initial query

## Next Steps

1. **Establish Baseline**: Run the test script to see current performance
2. **Monitor Logs**: Check the application logs for performance metrics
3. **Identify Bottlenecks**: Use the timing breakdown to find the slowest components
4. **Optimize**: Focus on the slowest operations first
5. **Re-test**: Measure improvements after optimizations

## Key Files Modified

1. `app/api/endpoints/team_requests.py` - Added endpoint-level logging
2. `app/crud/crud_team_request.py` - Added CRUD-level logging
3. `app/db/session.py` - Added SQL query logging
4. `test_team_requests_performance.py` - Performance testing script
5. `TEAM_REQUESTS_PERFORMANCE_ANALYSIS.md` - Detailed analysis guide

## Log Examples

### Successful Request
```
[TEAM_REQUESTS_PERF] Starting read_team_requests for user abc123 (org: def456)
[TEAM_REQUESTS_PERF] Permission checks completed in 0.001s
[CRUD_PERF] Starting get_all_with_filters for org def456, filters: {'active': None}
[CRUD_PERF] Base query construction completed in 0.002s
[CRUD_PERF] Query execution completed in 0.245s, returned 150 records
[CRUD_PERF] get_all_with_filters total time: 0.247s
[TEAM_REQUESTS_PERF] Private requests query completed in 0.247s, returned 150 records
[CRUD_PERF] Starting get_community for org def456
[CRUD_PERF] get_community query execution completed in 0.850s, returned 75 records
[TEAM_REQUESTS_PERF] Community requests query completed in 0.850s, returned 75 records
[TEAM_REQUESTS_PERF] Private requests serialization completed in 0.045s
[TEAM_REQUESTS_PERF] Community requests serialization completed in 0.032s
[TEAM_REQUESTS_PERF] Total request completed in 1.175s
```

### Slow Query Alert
```
[SQL_PERF] Slow query (1.234s): WITH Deduplicated AS ( SELECT "teamId", "position", transfer_period, ARRAY_AGG(DISTINCT created_by) AS created_by_array...
```

This comprehensive logging system will help you identify exactly where the performance bottlenecks are in the team requests endpoint and guide your optimization efforts.
