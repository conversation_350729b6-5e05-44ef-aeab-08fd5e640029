from .staff_record import <PERSON><PERSON><PERSON><PERSON>, StaffRecordShort
from .player_record import <PERSON><PERSON><PERSON><PERSON>, PlayerRecordShort
from .contract import (
    Contract,
    ContractShort,
)
from app.schemas.football_field import (
    FootballFieldCreate,
    FootballFieldUpdate,
    FootballFieldAutofill,
)
from app.schemas.request_field import (
    RequestFieldCreate,
    RequestFieldUpdate,
    RequestFieldAutofill,
    RequestField,
    RequestFieldShare
)
from app.schemas.field_players import FieldPlayersCreate
from app.schemas.field_requests import FieldRequestsCreate, FieldRequests

StaffRecordShort.update_forward_refs()
StaffRecord.update_forward_refs()

PlayerRecordShort.update_forward_refs()
PlayerRecord.update_forward_refs()

ContractShort.update_forward_refs(
    StaffRecordShort=StaffRecordShort,
    PlayerRecordShort=PlayerRecordShort
)
Contract.update_forward_refs(
    StaffRecordShort=StaffRecordShort,
    PlayerRecordShort=PlayerRecordShort
)
FootballFieldUpdate.update_forward_refs(FieldPlayersCreate=FieldPlayersCreate)
FootballFieldAutofill.update_forward_refs(FieldPlayersCreate=FieldPlayersCreate)
FootballFieldCreate.update_forward_refs(FieldPlayersCreate=FieldPlayersCreate)

RequestFieldUpdate.update_forward_refs(FieldRequestsCreate=FieldRequestsCreate)
RequestFieldAutofill.update_forward_refs(FieldRequestsCreate=FieldRequestsCreate)
RequestFieldCreate.update_forward_refs(FieldRequestsCreate=FieldRequestsCreate)
RequestField.update_forward_refs(FieldRequests=FieldRequests)
RequestFieldShare.update_forward_refs(FieldRequests=FieldRequests)