from app.config import settings

def check_contract_expiry(con, schema, date_threshold=14):
    out = con.execute(
    f"""
    select *
    from {schema}.contracts
    where end_date::date - now()::date between 0 and {date_threshold}
    """
    )

    return out


def check_birthdate(con, schema, date_threshold=14):
    out = con.execute(
    f"""
    select ns.*
    from {schema}.player_records pr, wyscout.{settings.PLAYER_INFO_VIEW_CRM} pi2, {schema}.notifications_settings ns, {schema}.user u
    where cast(pi2.birthDate::date + ((extract(year from age(pi2.birthDate::date)) + 1) * interval '1' year) as date) - current_date between 0 and {date_threshold}
    and pi2."playerId" = pr."playerId"
    and pr.id = ns.player_id
    and ns.player_notifications = true
    and ns.user_id = u.id
    and pr.organization_id = u.organization_id 
    """
    )

    return out