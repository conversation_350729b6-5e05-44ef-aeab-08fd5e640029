import uuid
from typing import Optional
from pydantic import BaseModel
from datetime import date
from app.schemas.player_info_view_system import PlayerInfoSystem

class FieldPlayersUpdate(BaseModel):
    field_id: Optional[uuid.UUID]
    playerId: Optional[int]
    teamId: Optional[int]
    x_cordinate: Optional[float]
    y_cordinate: Optional[float]
    suitability_score: Optional[float]
    index_position: Optional[int]
    transfer_fee: Optional[float]
    asking_salary: Optional[float]
    video_link: Optional[str]
    contract_expiry: Optional[date]
    foot: Optional[str]
    description: Optional[str]
    class Config:
        use_cache=True
        orm_mode = True


class FieldPlayersCreate(FieldPlayersUpdate):
    playerId: int
    suitability_score: Optional[float]
    x_cordinate: float
    y_cordinate: float
    transfer_fee: Optional[float]
    asking_salary: Optional[float]
    video_link: Optional[str]
    contract_expiry: Optional[date]
    foot: Optional[str]
    description: Optional[str]

class FieldPlayers(BaseModel):
    id: uuid.UUID
    player: PlayerInfoSystem
    playerId: int
    suitability_score: float
    x_cordinate: Optional[float]
    y_cordinate: Optional[float]
    transfer_fee: Optional[float]
    asking_salary: Optional[float]
    video_link: Optional[str]
    contract_expiry: Optional[date]
    foot: Optional[str]
    description: Optional[str]
    class Config:
        orm_mode = True

class FieldPlayersAutofill(BaseModel):
    player: PlayerInfoSystem
    playerId: int
    suitability_score: float
    index_position: Optional[int]
    x_cordinate: float
    y_cordinate: float
    transfer_fee: Optional[float]
    asking_salary: Optional[float]
    video_link: Optional[str]
    contract_expiry: Optional[date]
    foot: Optional[str]
    description: Optional[str]

    class Config:
        orm_mode = True
        use_cache=True

