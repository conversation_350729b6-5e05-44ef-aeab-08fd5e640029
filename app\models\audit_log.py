from datetime import datetime
import uuid

from sqlalchemy import Column, String, DateTime, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.config import settings


class AuditLog(Base):
    __tablename__ = "audit_logs"
    __table_args__ = {"schema": settings.PG_SCHEMA}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    edit_at = Column(DateTime, index=True, default=datetime.now, nullable=False)
    
    # Action information
    action_type = Column(String, nullable=False, index=True)  # create, update, delete, proposed, gave_feedback
    record_type = Column(String, nullable=False, index=True)  # player, request, activity, shadow_squad, etc.
    record_id = Column(UUID(as_uuid=True), nullable=False, index=True)  # ID of the affected record
    record_name = Column(String, nullable=True)  # Human-readable identifier (player name, activity title, etc.)
    
    # Change data - using JSONB for better querying and storage
    previous = Column(JSONB, nullable=True)  # Previous state of the record/field
    updated = Column(JSONB, nullable=True)   # New state of the record/field
    
    # User and organization context
    edit_by = Column(
        UUID(as_uuid=True), 
        ForeignKey(f"{settings.PG_SCHEMA}.user.id"), 
        nullable=False, 
        index=True
    )
    organization_id = Column(
        UUID(as_uuid=True), 
        ForeignKey(f"{settings.PG_SCHEMA}.organizations.id"), 
        nullable=False, 
        index=True
    )
    
    # Additional audit fields
    ip_address = Column(String, nullable=True)  # For security auditing
    user_agent = Column(Text, nullable=True)    # Browser/client information
    batch_id = Column(UUID(as_uuid=True), nullable=True, index=True)  # Group related changes
    
    # Additional context
    details = Column(Text, nullable=True)  # Additional details about the change
    
    # Relationships
    editor = relationship("User", foreign_keys=[edit_by])
    organization = relationship("Organization", foreign_keys=[organization_id])

    def __repr__(self):
        return f"<AuditLog(action={self.action_type}, record_type={self.record_type}, record_id={self.record_id})>"
