from datetime import datetime
import uuid

from sqlalchemy import Column, ForeignKey, String, DateTime
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from app.db.base_class import Base
from app.config import settings


class AdminChange(Base):
    __tablename__ = "admin_changes"
    __table_args__ = {"schema": settings.PG_SCHEMA}

    # Base fields (from the old ChangeBaseMixin)
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    edit_at = Column(DateTime, index=True, default=datetime.now)
    field = Column(String)
    edit_by = Column(
        UUID(as_uuid=True), ForeignKey(f"{settings.PG_SCHEMA}.user.id"), index=True
    )
    editor = relationship("User", foreign_keys=[edit_by])
    previous = Column(String)
    updated = Column(String)

    # Additional fields specific to admin changes
    action_type = Column(
        String, nullable=False
    )  # e.g. "create_user", "delete_organization", etc.
    target_type = Column(String, nullable=False)  # e.g. "user", "organization", etc.
    target_id = Column(UUID(as_uuid=True), nullable=False)  # ID of the affected entity
    details = Column(String, nullable=True)  # Additional details about the change
