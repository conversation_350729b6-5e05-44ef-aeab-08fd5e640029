"""Priority players on delete cascade

Revision ID: 98a7a884fca6
Revises: 38a455d849f1
Create Date: 2025-08-26 16:21:51.926217

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '98a7a884fca6'
down_revision = '38a455d849f1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('priority_players_player_record_id_fkey', 'priority_players', schema='crm_test', type_='foreignkey')
    op.create_foreign_key(None, 'priority_players', 'player_records', ['player_record_id'], ['id'], source_schema='crm_test', referent_schema='crm_test', ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'priority_players', schema='crm_test', type_='foreignkey')
    op.create_foreign_key('priority_players_player_record_id_fkey', 'priority_players', 'player_records', ['player_record_id'], ['id'], source_schema='crm_test', referent_schema='crm_test')
    # ### end Alembic commands ###