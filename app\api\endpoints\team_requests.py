from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Body, Request
from sqlalchemy.orm import Session
from fastapi.params import Query
from app.schemas.team_request import (
    TeamRequest,
    TeamRequestUpdate,
    TeamRequestCreate,
    CommunityTeamRequest,
)
from app.api.endpoints.platform_notifications import create_in_app_notifications_bulk
from app.schemas.tracked_transfers import TrackedTransfer, TrackedTransferUpdate
from app.schemas.community_tokens import CommunityTokensUpdate
from app.schemas.tracked_transfers import TrackedTransfer, TrackedTransferUpdate
from app.schemas.community_proposal import CommunityProposalCreate, CommunityProposal
from app.schemas.user import User
from app import crud, models
from app.api import deps, utils
from app.utils.audit_decorator import audit_action
from app.utils.audit_logging import (
    log_create_event,
)
from fastapi.encoders import jsonable_encoder
import uuid
try:
    from ...config import settings
except:
    from app.config import settings
from app.api.endpoints.contacts import read_contacts
from app.utils import mail, caching, get_async_response, community_tokens
from app.utils.whatsapp_bot_security import (
    validate_whatsapp_bot_request,
    check_rate_limit,
    verify_api_key,
)
from app.api.endpoints.message_subscription import (
    read_subscriptions,
    read_subscriptions_email,
)

router = APIRouter()


def serialize_team_request_for_mail(team_request):
    """Serialize a TeamRequest object for background mail tasks to avoid DetachedInstanceError"""
    return {
        "id": team_request.id,
        "position": team_request.position,
        "foot": team_request.foot,
        "max_age": team_request.max_age,
        "max_value": team_request.max_value,
        "max_net_salary": team_request.max_net_salary,
        "transfer_period": team_request.transfer_period,
        "type": team_request.type,
        "description": team_request.description,
        "eu_passport": team_request.eu_passport,
        "team_info": {
            "teamId": team_request.team_info.teamId,
            "name": team_request.team_info.name,
            "area_name": team_request.team_info.area_name,
        },
        "creator": {
            "email": team_request.creator.email,
        },
        "source_to_record": [
            {
                "source": {
                    "first_name": x.source.first_name,
                    "last_name": x.source.last_name,
                }
            }
            for x in team_request.source_to_record
        ] if team_request.source_to_record else None,
    }


@router.get("/")
async def read_team_requests(
    db: Session = Depends(deps.get_db),
    active: Optional[bool] = Query(None),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve Team requests.
    """
    utils.check_get_all(TeamRequest, current_user)
    can_access_sense = utils.can_access_sensitive(current_user)

    # Possibly exclude some fields
    exclude = {} if can_access_sense else settings.TEAM_REQUEST_SENSITIVE_FIELDS
    if current_user.role.name == "viewer":
        exclude.update(settings.TEAM_REQUEST_VIEWERS_FILTER_FIELDS)

    filters = {"active": active}
    private_requests = crud.team_request.get_all_with_filters(
        db,
        current_user.organization_id,
        can_access_sense,
        filters,
    )

    community_requests = crud.team_request.get_community(
        db, current_user.organization_id, can_access_sense
    )

    return_requests = {
        "private": [
            TeamRequest.from_orm(x).dict(exclude=exclude) for x in private_requests
        ],
        "community": [
            CommunityTeamRequest.from_orm(x).dict(exclude=exclude)
            for x in community_requests
        ],
    }
    return return_requests


@router.get(
    "/wyscout/{teamId}",
)
async def read_requests_for_a_team(
    teamId: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve Team requests.
    """
    utils.check_get_all(TeamRequest, current_user)
    can_access_sense = utils.can_access_sensitive(current_user)
    exclude = {} if can_access_sense else settings.TEAM_REQUEST_SENSITIVE_FIELDS
    if current_user.role.name == "viewer":
        exclude.update(settings.TEAM_REQUEST_VIEWERS_FILTER_FIELDS)

    return_requests = crud.team_request.get_all_for_team(
        db=db,
        teamId=teamId,
        org_id=current_user.organization_id,
        can_access_sensitive=utils.can_access_sensitive(current_user),
    )

    return [TeamRequest.from_orm(x).dict(exclude=exclude) for x in return_requests]


@router.post("/", response_model=TeamRequest)
@audit_action(action_type="create", record_type="team_request")
async def create_team_request(
    *,
    db: Session = Depends(deps.get_db),
    team_request_in: TeamRequestCreate,
    background_tasks: BackgroundTasks,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create a new Team request.
    """
    if team_request_in.is_community:
        if "community_requests" not in utils.get_user_modules(current_user):
            raise HTTPException(status_code=403, detail="Not authorized")
        tokens_left = crud.community_tokens.get_for_org(
            db=db, organization_id=current_user.organization_id
        )
        if community_tokens["upload"] > tokens_left.tokens:
            raise HTTPException(status_code=402, detail="Not enough tokens")
        else:
            tokens_in = tokens_left
            tokens_in.tokens -= community_tokens["upload"]
            crud.community_tokens.update(
                db=db,
                db_obj=tokens_left,
                obj_in=CommunityTokensUpdate.from_orm(tokens_in),
            )

    utils.check_create(TeamRequest, current_user)
    team_request = crud.team_request.create_with_user(
        db=db,
        obj_in=team_request_in,
        user=current_user,
    )
    if team_request_in.emails:
        background_tasks.add_task(
            mail.send_mail_new_request,
            [serialize_team_request_for_mail(team_request)],
            current_user=current_user,
            db=db,
            read_contacts_func=read_contacts,
            read_subscription_func=read_subscriptions_email,
            emails=team_request_in.emails,
            create_in_app_notifications_bulk=create_in_app_notifications_bulk,
        )

    return team_request


@router.post("/community_request")
@audit_action(action_type="create", record_type="community_proposal")
async def send_new_proposal(
    *,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    community_proposal_in: CommunityProposalCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
    suitability_score: str = Body(...),
) -> Any:
    """
    Send a new proposal to a community request
    """
    # print(utils.get_user_modules(current_user))
    if "community_requests" not in utils.get_user_modules(current_user):
        raise HTTPException(status_code=403, detail="Not authorized")
    utils.check_create(CommunityProposal, current_user)

    tokens_left = crud.community_tokens.get_for_org(
        db=db, organization_id=current_user.organization_id
    )
    if community_tokens["propose"] > tokens_left.tokens:
        raise HTTPException(status_code=402, detail="Not enough tokens")
    else:
        tokens_in = tokens_left
        tokens_in.tokens -= community_tokens["propose"]
        crud.community_tokens.update(
            db=db,
            db_obj=tokens_left,
            obj_in=CommunityTokensUpdate.from_orm(tokens_in),
        )

    request = crud.team_request.get(db=db, id=community_proposal_in.request_id)
    player = crud.player_record.get(db=db, id=community_proposal_in.player_id)
    mins_played = 0
    try:
        mins_played = crud.player_record.get_minutes_played(
            db=db, wyId=player.player_info_view_crm.playerId
        )["total_mins_in_year"]
    except:
        print(f"No minutes played for player - {player.player_info_view_crm.playerId}")

    phone_number = None
    if community_proposal_in.phone_number:
        phone_number = community_proposal_in.phone_number
    del community_proposal_in.phone_number

    community_proposal = crud.community_proposal.create_with_user(
        db=db,
        obj_in=community_proposal_in,
        user=current_user,
    )
    dd = {}
    receiver_emails = []
    for id in community_proposal_in.request_creator:
        request_creator = crud.user.get(id=id, db=db)
        receiver_emails.append(request_creator.email)
        dd = crud.community_deal.create_double_deal(
            db=db,
            proposal_obj=community_proposal,
            user=current_user,
            request_creator=request_creator,
            suitability_score=suitability_score,
            phone_number=phone_number,
        )
    community_dict = {
        "are_you_the_agent": community_proposal.are_you_the_agent,
        "description": community_proposal.description,
        "expected_salary": community_proposal.expected_salary,
        "club_asking_price": community_proposal.club_asking_price,
        "video_link": community_proposal.video_link,
    }
    request_dict = {
        "team_info": {
            "name": request.team_info.name,
            "teamId": request.team_info.teamId,
        },
        "position": request.position,
        "created_by": request.created_by,
    }
    player_dict = {
        "player_info_view_crm": {
            "passportArea_name": player.player_info_view_crm.passportArea_name,
            "birthArea_name": player.player_info_view_crm.birthArea_name,
            "tm_link": player.player_info_view_crm.player_url,
            "firstName": player.player_info_view_crm.firstName,
            "lastName": player.player_info_view_crm.lastName,
            "team_name": player.player_info_view_crm.team_name,
        },
    }
    background_tasks.add_task(
        mail.send_mail_new_proposal,
        request_dict,
        receiver_emails,
        player_dict,
        community_dict,
        dd["received"].id,
        current_user=current_user,
        mins_played=mins_played,
        suitability_score=suitability_score,
        read_subscription_func=read_subscriptions,
        db=db,
        create_in_app_notifications_bulk=create_in_app_notifications_bulk,
    )

    return community_proposal


@router.post(
    "/bulk/",
    response_description="Add multiple team requests at once",
    response_model=List[TeamRequest],
)
@audit_action(action_type="create", record_type="team_request")
async def create_bulk_requests(
    background_tasks: BackgroundTasks,
    request: Request,
    db: Session = Depends(deps.get_db),
    team_request_list: List[TeamRequestCreate] = Body(...),
    current_user: models.User = Depends(deps.get_current_active_user),
):

    if team_request_list[0].is_community:
        if "community_requests" not in utils.get_user_modules(current_user):
            raise HTTPException(status_code=403, detail="Not authorized")
        tokens_left = crud.community_tokens.get_for_org(
            db=db, organization_id=current_user.organization_id
        )
        if community_tokens["upload"] * len(team_request_list) > tokens_left.tokens:
            raise HTTPException(status_code=402, detail="Not enough tokens")
        else:
            tokens_in = tokens_left
            tokens_in.tokens -= community_tokens["upload"] * len(team_request_list)
            crud.community_tokens.update(
                db=db,
                db_obj=tokens_left,
                obj_in=CommunityTokensUpdate.from_orm(tokens_in),
            )

    utils.check_create(TeamRequest, current_user)
    team_requests = crud.team_request.bulk_create_with_user(
        db=db, objs_in=team_request_list, user=current_user
    )

    if team_request_list[0].emails:
        background_tasks.add_task(
            mail.send_mail_new_request,
            [serialize_team_request_for_mail(tr) for tr in team_requests],
            current_user=current_user,
            db=db,
            read_contacts_func=read_contacts,
            read_subscription_func=read_subscriptions_email,
            emails=team_request_list[0].emails,
            create_in_app_notifications_bulk=create_in_app_notifications_bulk,
        )
    return team_requests


@router.post(
    "/whatsapp/bulk",
    response_description="Add multiple team requests at once from whatsapp",
)
async def create_whatsapp_bulk_requests(
    phone_number: str,
    request: Request,
    db: Session = Depends(deps.get_db),
    team_request_list: List[TeamRequestCreate] = Body(...),
    api_key_valid: bool = Depends(verify_api_key),
):
    # Rate limiting check
    check_rate_limit(phone_number)

    # Validate WhatsApp bot request and get user
    current_user = await validate_whatsapp_bot_request(phone_number, db)
    utils.check_create(TeamRequest, current_user)

    tokens_left = crud.community_tokens.get_for_org(
        db=db, organization_id=current_user.organization_id
    )
    if community_tokens["request_whatsapp"] > tokens_left.tokens:
        raise HTTPException(status_code=402, detail={"tokens_left": tokens_left.tokens})
    else:
        tokens_in = tokens_left
        tokens_in.tokens -= community_tokens["request_whatsapp"]
        crud.community_tokens.update(
            db=db,
            db_obj=tokens_left,
            obj_in=CommunityTokensUpdate.from_orm(tokens_in),
        )

    for t in team_request_list:
        t.source_to_record = [current_user.id]
    team_requests = crud.team_request.bulk_create_with_user(
        db=db, objs_in=team_request_list, user=current_user
    )
    for request in team_requests:
        log_create_event(
            db=db,
            user=current_user,
            record_type="team_request",
            record_id=request.id,
            record_data=jsonable_encoder(request),
            details="WhatsApp bulk request creation",
            request=request,
        )

    return {"tokens_left": tokens_left.tokens}


@router.put("/{id}", response_model=TeamRequest)
@audit_action(
    action_type="update",
    record_type="team_request",
    get_original_object=lambda kwargs: crud.team_request.get_by_org(
        kwargs['db'], kwargs['id'], kwargs['current_user'].organization_id
    )
)
async def update_team_request(
    *,
    db: Session = Depends(deps.get_db),
    request: Request,
    id: str,
    team_request_in: TeamRequestUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update an Team request.
    """

    team_request = crud.team_request.get_by_org(
        db=db, id=id, org_id=current_user.organization_id
    )
    utils.check_modify(team_request, current_user)
    if team_request_in.is_community and not team_request.is_community:
        if "community_requests" not in utils.get_user_modules(current_user):
            raise HTTPException(status_code=403, detail="Not authorized")
        tokens_left = crud.community_tokens.get_for_org(
            db=db, organization_id=current_user.organization_id
        )
        if community_tokens["upload"] > tokens_left.tokens:
            raise HTTPException(status_code=402, detail="Not enough tokens")
        else:
            tokens_in = tokens_left
            tokens_in.tokens -= community_tokens["upload"]
            crud.community_tokens.update(
                db=db,
                db_obj=tokens_left,
                obj_in=CommunityTokensUpdate.from_orm(tokens_in),
            )


    team_request = crud.team_request.update(
        db=db, db_obj=team_request, obj_in=team_request_in
    )
    return jsonable_encoder(team_request)


@router.put("/tracked_transfer/{id}", response_model=TrackedTransfer)
@audit_action(
    action_type="update",
    record_type="tracked_transfer",
    get_original_object=lambda kwargs: crud.tracked_transfer.get(
        kwargs['db'], kwargs['id']
    )
)
async def update_tracked_transfer(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    tracked_transfer_in: TrackedTransferUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a Tracked Transfer record.
    """
    tracked_transfer = crud.tracked_transfer.get(db=db, id=id)
    utils.check_modify(tracked_transfer, current_user)

    tracked_transfer = crud.tracked_transfer.update(
        db=db, db_obj=tracked_transfer, obj_in=tracked_transfer_in
    )
    return tracked_transfer


@router.get("/{id}", response_model=TeamRequest)
def read_team_request(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get TeamRequest by ID.
    """
    team_request = crud.team_request.get(db=db, id=id)
    utils.check_get_one(team_request, current_user)
    if (
        team_request
        and team_request.team_info
        and team_request.is_community
        and team_request.organization_id != current_user.organization_id
    ):
        team_request.team_info.name = ""

    return team_request


@router.delete("/bulk-delete", response_model=List[TeamRequest])
@audit_action(action_type="delete", record_type="team_request",)
async def delete_multiple_team_requests(
    *,
    db: Session = Depends(deps.get_db),
    request: Request,
    ids: Optional[str] = None,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete multiple Team requests.
    """
    if ids is None:
        raise HTTPException(status_code=400, detail="No ids provided")

    ids_list = ids.split(",")
    team_requests_out = []
    for id in ids_list:
        team_request = crud.team_request.get_by_org(
            db=db, id=id, org_id=current_user.organization_id
        )
        utils.check_delete(team_request, current_user)
        team_request_out = TeamRequest.from_orm(team_request)
        team_requests_out.append(team_request_out)
        crud.team_request.remove(db=db, id=id)
    return team_requests_out


@router.delete("/{id}", response_model=TeamRequest)
@audit_action(action_type="delete", record_type="team_request",)
async def delete_team_request(
    *,
    db: Session = Depends(deps.get_db),
    request: Request,
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a Team request.
    """
    team_request = crud.team_request.get_by_org(
        db=db, id=id, org_id=current_user.organization_id
    )
    utils.check_delete(team_request, current_user)
    team_request_out = TeamRequest.from_orm(team_request)
    crud.team_request.remove(db=db, id=id)
    return team_request_out


@router.get(
    "/image/{teamId}",
)
async def get_team_image(
    *,
    teamId: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get Wyscout Image by wyscout Team ID.
    """

    # wyscout = requests.get(
    #         f"https://apirest.wyscout.com/v3/teams/{teamId}",
    #         auth=(settings.WYSCOUT_USR, settings.WYSCOUT_PASS),
    #         params={"imageDataURL": True},
    #     )

    wyscout = await get_async_response(
        f"https://apirest.wyscout.com/v3/teams/{teamId}",
        auth=(settings.WYSCOUT_USR, settings.WYSCOUT_PASS),
        params={"imageDataURL": True},
    )

    return wyscout.json()["imageDataURL"]
