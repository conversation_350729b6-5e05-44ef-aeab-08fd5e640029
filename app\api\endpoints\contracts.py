from typing import Any, List, Optional

from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session

from app.schemas.contract import Contract, ContractUpdate, ContractCreate
from app import crud, models
from app.api import deps, utils
from app.schemas.enums import ContractType
from fastapi.params import Query
from app.utils.audit_decorator import audit_action

router = APIRouter()


def make_contract_crud(contract_type: ContractType) -> crud.CRUDContract:
    if model := next(
        filter(
            lambda x: x.__mapper_args__["polymorphic_identity"] == contract_type,
            {
                models.Mandate,
                models.RepresentationAgreement,
                models.ClubContract,
                models.CommissionAgreement,
                models.Commercial,
            },
        ),
        None,
    ):
        return crud.CRUDContract(model)
    raise ValueError("Invalid contract type")


@router.get("/", response_model=List[Contract])
def read_contracts(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    contract_type: Optional[str] = Query(None),
) -> Any:
    """
    Retrieve Contracts.
    """
    utils.check_module_access(current_user, "legal")
    utils.check_get_all(Contract, current_user)
    contract_type = contract_type.split(",") if contract_type else None
    filters = {"contract_type": contract_type}
    return crud.contract.get_all_with_filters(
        db,
        current_user.organization_id,
        utils.can_access_sensitive(current_user),
        filters,
    )


@router.post("/", response_model=Contract)
@audit_action(action_type="create", record_type="contract")
def create_contract(
    *,
    db: Session = Depends(deps.get_db),
    contract_in: ContractCreate,
    request: Request,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new Contract.
    """
    utils.check_module_access(current_user, "legal")
    utils.check_create(Contract, current_user)
    specific_crud = make_contract_crud(contract_in.contract_type)
    contract = specific_crud.create_with_user(
        db=db,
        obj_in=contract_in,
        user=current_user,
    )
    return contract


@router.put("/{id}", response_model=Contract)
@audit_action(
    action_type="update",
    record_type="contract",
    get_original_object=lambda kwargs: crud.contract.get_by_org(
        kwargs['db'], kwargs['id'], kwargs['current_user'].organization_id
    )
)
def update_contract(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    request: Request,
    contract_in: ContractUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update an Contract.
    """
    utils.check_module_access(current_user, "legal")
    contract = crud.contract.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_modify(contract, current_user)
    specific_crud = make_contract_crud(contract.contract_type)
    contract = specific_crud.update(db=db, db_obj=contract, obj_in=contract_in)
    return contract


@router.get("/{id}", response_model=Contract)
def read_contract(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get Contract by ID.
    """
    utils.check_module_access(current_user, "legal")
    contract = crud.contract.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_get_one(contract, current_user)
    return contract


@router.get("/player/{player_id}", response_model=List[Contract])
def read_contract_for_a_player(
    *,
    db: Session = Depends(deps.get_db),
    player_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get All contracts by player_id.
    """
    utils.check_module_access(current_user, "legal")
    utils.check_get_all(Contract, current_user)
    contracts = crud.contract.get_all_filtered_for_player_org(db=db, id=player_id, org_id=current_user.organization_id)
    return contracts


@router.get("/staff/{staff_record_id}", response_model=List[Contract])
def read_contract_for_a_staff(
    *,
    db: Session = Depends(deps.get_db),
    staff_record_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get All contracts by staff_record_id.
    """
    utils.check_module_access(current_user, "legal")
    utils.check_get_all(Contract, current_user)
    contracts = crud.contract.get_all_filtered_for_staff_org(db=db, id=staff_record_id, org_id=current_user.organization_id)
    return contracts


@router.get("/team/{teamId}", response_model=List[Contract])
def read_contract_for_a_team(
    *,
    db: Session = Depends(deps.get_db),
    teamId: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get All contracts by teamId.
    """
    utils.check_module_access(current_user, "legal")
    utils.check_get_all(Contract, current_user)
    contracts = crud.contract.get_all_filtered_for_team(db=db, id=teamId, org_id=current_user.organization_id)
    return contracts


@router.delete("/{id}", response_model=Contract)
@audit_action(
    action_type="delete",
    record_type="contract",
)
def delete_contract(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    request: Request,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a contract.
    """
    utils.check_module_access(current_user, "legal")
    contract = crud.contract.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    utils.check_delete(contract, current_user)
    specific_crud = make_contract_crud(contract.contract_type)
    contract_out = Contract.from_orm(contract)
    specific_crud.remove(db=db, id=id)
    return contract_out
