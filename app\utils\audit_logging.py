import uuid
from typing import Any, Dict, List, Optional, Union
from sqlalchemy.orm import Session
from fastapi import Request
from fastapi.encoders import jsonable_encoder

from app import models
from app.crud.crud_audit_log import audit_log
from app.schemas.audit_log import AuditLogCreate
from app.utils import excluded_fields


def get_record_name(record_type: str, record_data: Dict[str, Any]) -> Optional[str]:
    """Extract a human-readable name from record data based on record type"""
    name_mappings = {
        "player": ["name", "player_name", "full_name", "player_info_view_crm"],
        "activity": ["title", "name"],
        "team_request": ["team_name", "name", "team_info"],
        "shadow_squad": ["name", "title"],
        "shadow_request": ["name", "title"],
        "contract": ["contract_type", "type"],
        "organization": ["name"],
        "user": ["email", "first_name", "last_name"],
        "contact": ["name", "email"],
        "proposal": ["title", "name"],
        "report": ["title", "name"],
    }

    possible_fields = name_mappings.get(record_type, ["name", "title"])

    for field in possible_fields:
        if field in record_data and record_data[field]:
            if field == "player_info_view_crm":
                return record_data[field].get("shortName") or record_data[field].get(
                    "firstName"
                ) + " " + record_data[field].get("lastName")
            if field == "team_info":
                return record_data[field].get("name")
            return str(record_data[field])

    return None


def extract_client_info(
    request: Optional[Request] = None,
) -> tuple[Optional[str], Optional[str]]:
    """Extract IP address and user agent from request"""
    if not request:
        return None, None

    # Get IP address (handle proxy headers)
    ip_address = None
    if hasattr(request, "client") and request.client:
        ip_address = request.client.host

    # Check for forwarded headers
    if hasattr(request, "headers"):
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            ip_address = forwarded_for.split(",")[0].strip()
        elif request.headers.get("X-Real-IP"):
            ip_address = request.headers.get("X-Real-IP")

    # Get user agent
    user_agent = None
    if hasattr(request, "headers"):
        user_agent = request.headers.get("User-Agent")

    return ip_address, user_agent


def log_audit_event(
    db: Session,
    *,
    user: models.User,
    action_type: str,
    record_type: str,
    record_id: Union[str, uuid.UUID],
    previous: Optional[Dict[str, Any]] = None,
    updated: Optional[Dict[str, Any]] = None,
    record_name: Optional[str] = None,
    details: Optional[str] = None,
    batch_id: Optional[uuid.UUID] = None,
    request: Optional[Request] = None,
) -> models.AuditLog:
    """
    Log an audit event with comprehensive details.

    Args:
        db: Database session
        user: The user performing the action
        action_type: Type of action (create, update, delete, proposed, gave_feedback)
        record_type: Type of record (player, request, activity, etc.)
        record_id: ID of the affected record
        previous: Previous state of the record/field
        updated: New state of the record/field
        record_name: Human-readable identifier (auto-extracted if not provided)
        details: Additional details about the change
        batch_id: Optional batch ID for grouping related changes
        request: FastAPI request object for extracting client info
    """
    # Auto-extract record name if not provided
    if not record_name and updated:
        record_name = get_record_name(record_type, updated)
    elif not record_name and previous:
        record_name = get_record_name(record_type, previous)

    # Extract client information
    ip_address, user_agent = extract_client_info(request)

    audit_entry = AuditLogCreate(
        action_type=action_type,
        record_type=record_type,
        record_id=record_id,
        record_name=record_name,
        previous=previous,
        updated=updated,
        details=details,
        batch_id=batch_id,
    )

    # Handle case where user is None (e.g., failed login attempts)
    if user is None:
        import uuid

        # Use a system user ID for failed logins
        system_user_id = uuid.UUID("00000000-0000-0000-0000-000000000000")
        system_org_id = uuid.UUID("00000000-0000-0000-0000-000000000000")
        # Create audit entry without user context
        audit_entry_dict = audit_entry.dict()
        audit_entry_dict.update(
            {
                "edit_by": system_user_id,
                "organization_id": system_org_id,
                "ip_address": ip_address,
                "user_agent": user_agent,
            }
        )
        db_obj = audit_log.model(**audit_entry_dict)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    else:
        return audit_log.create_with_user(
            db=db,
            obj_in=audit_entry,
            user=user,
            ip_address=ip_address,
            user_agent=user_agent,
        )


def log_create_event(
    db: Session,
    *,
    user: models.User,
    record_type: str,
    record_id: Union[str, uuid.UUID],
    record_data: Dict[str, Any],
    details: Optional[str] = None,
    batch_id: Optional[uuid.UUID] = None,
    request: Optional[Request] = None,
) -> models.AuditLog:
    """Log a create event"""
    return log_audit_event(
        db=db,
        user=user,
        action_type="create",
        record_type=record_type,
        record_id=record_id,
        updated=record_data,
        details=details,
        batch_id=batch_id,
        request=request,
    )


def log_update_event(
    db: Session,
    *,
    user: models.User,
    record_type: str,
    record_id: Union[str, uuid.UUID],
    previous_data: Dict[str, Any],
    updated_data: Dict[str, Any],
    details: Optional[str] = None,
    batch_id: Optional[uuid.UUID] = None,
    request: Optional[Request] = None,
) -> models.AuditLog:
    """Log an update event"""
    return log_audit_event(
        db=db,
        user=user,
        action_type="update",
        record_type=record_type,
        record_id=record_id,
        previous=previous_data,
        updated=updated_data,
        details=details,
        batch_id=batch_id,
        request=request,
    )


def log_delete_event(
    db: Session,
    *,
    user: models.User,
    record_type: str,
    record_id: Union[str, uuid.UUID],
    record_data: Dict[str, Any],
    details: Optional[str] = None,
    batch_id: Optional[uuid.UUID] = None,
    request: Optional[Request] = None,
) -> models.AuditLog:
    """Log a delete event"""
    return log_audit_event(
        db=db,
        user=user,
        action_type="delete",
        record_type=record_type,
        record_id=record_id,
        previous=record_data,
        updated=None,
        details=details,
        batch_id=batch_id,
        request=request,
    )


def clean_assigned_to_record_data(data: Any) -> Optional[List[str]]:
    """
    Extract only contact_id values from assigned_to_record data.

    Args:
        data: Can be a list of objects with contact_id, or None

    Returns:
        List of contact_id strings, or None if no data
    """
    if not data:
        return None

    if isinstance(data, list):
        contact_ids = []
        for item in data:
            if isinstance(item, dict) and "contact_id" in item:
                contact_ids.append(str(item["contact_id"]))
            elif hasattr(item, "contact_id"):
                contact_ids.append(str(item.contact_id))
            elif isinstance(item, uuid.UUID):
                contact_ids.append(str(item))
            elif isinstance(item, str):
                contact_ids.append(str(item))
        return contact_ids if contact_ids else None

    return None


def normalize_value(value):
    """Normalize values for consistent comparison"""
    from datetime import date, datetime

    if value is None or value == []:
        return None

    # Handle date/datetime fields - convert to ISO string for comparison
    if isinstance(value, (date, datetime)):
        return value.isoformat() if hasattr(value, "isoformat") else str(value)

    # Handle string representations of dates/datetimes
    if isinstance(value, str):
        # Try to convert string numbers to appropriate types
        if value.isdigit():
            try:
                return int(value)
            except ValueError:
                pass
        else:
            try:
                # Try to convert string numbers to float
                if "." in value:
                    return float(value)
            except ValueError:
                pass

    return value


def compare_and_log_changes(
    db: Session,
    *,
    user: models.User,
    record_type: str,
    record_id: Union[str, uuid.UUID],
    old_obj: Any,
    new_data: Dict[str, Any],
    exclude_fields: Optional[list] = None,
    batch_id: Optional[uuid.UUID] = None,
    request: Optional[Request] = None,
) -> Optional[models.AuditLog]:
    """
    Compare old object with new data and log changes if any.

    Args:
        db: Database session
        user: User performing the action
        record_type: Type of record
        record_id: ID of the record
        old_obj: Original SQLAlchemy object
        new_data: New data dictionary
        exclude_fields: Fields to exclude from comparison
        batch_id: Optional batch ID
        request: FastAPI request object
    """
    exclude_fields = exclude_fields or excluded_fields

    # Convert old object to dict
    old_data = jsonable_encoder(old_obj)

    # Find changes
    changes = {}
    previous_values = {}
    for field, new_value in new_data.items():
        if field in exclude_fields:
            continue

        old_value = old_data.get(field)
        # Special handling for assigned_to_record - extract only contact_ids
        if field == "assigned_to_record":
            old_contact_ids = clean_assigned_to_record_data(old_value)
            new_contact_ids = clean_assigned_to_record_data(new_value)

            if old_contact_ids != new_contact_ids:
                changes[field] = new_contact_ids
                previous_values[field] = old_contact_ids
        else:
            # Normalize values before comparison to handle type conversions and dates
            normalized_old = normalize_value(old_value)
            normalized_new = normalize_value(new_value)

            if normalized_old != normalized_new:
                changes[field] = new_value
                previous_values[field] = old_value

    # Only log if there are actual changes
    if changes:
        return log_update_event(
            db=db,
            user=user,
            record_type=record_type,
            record_id=record_id,
            previous_data=previous_values,
            updated_data=changes,
            batch_id=batch_id,
            request=request,
        )

    return None
