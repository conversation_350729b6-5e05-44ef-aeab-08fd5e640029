"""Audit log - implemented

Revision ID: 7087deea4f5d
Revises: 80cdb12f33ea
Create Date: 2025-08-25 15:14:23.136608

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '7087deea4f5d'
down_revision = '80cdb12f33ea'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('audit_logs',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('edit_at', sa.DateTime(), nullable=False),
    sa.Column('action_type', sa.String(), nullable=False),
    sa.Column('record_type', sa.String(), nullable=False),
    sa.Column('record_id', sa.UUID(), nullable=False),
    sa.Column('record_name', sa.String(), nullable=True),
    sa.Column('previous', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('updated', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('edit_by', sa.UUID(), nullable=False),
    sa.Column('organization_id', sa.UUID(), nullable=False),
    sa.Column('ip_address', sa.String(), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('batch_id', sa.UUID(), nullable=True),
    sa.Column('details', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['edit_by'], ['crm_test.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_test.organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_audit_logs_action_type'), 'audit_logs', ['action_type'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_audit_logs_batch_id'), 'audit_logs', ['batch_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_audit_logs_edit_at'), 'audit_logs', ['edit_at'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_audit_logs_edit_by'), 'audit_logs', ['edit_by'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_audit_logs_organization_id'), 'audit_logs', ['organization_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_audit_logs_record_id'), 'audit_logs', ['record_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_audit_logs_record_type'), 'audit_logs', ['record_type'], unique=False, schema='crm_test')
    op.drop_index('idx_atr_activity', table_name='assigned_to_record', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_atr_activity', 'assigned_to_record', ['activity_id', 'contact_id'], unique=False, schema='crm_test')
    op.drop_index(op.f('ix_crm_test_audit_logs_record_type'), table_name='audit_logs', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_audit_logs_record_id'), table_name='audit_logs', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_audit_logs_organization_id'), table_name='audit_logs', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_audit_logs_edit_by'), table_name='audit_logs', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_audit_logs_edit_at'), table_name='audit_logs', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_audit_logs_batch_id'), table_name='audit_logs', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_audit_logs_action_type'), table_name='audit_logs', schema='crm_test')
    op.drop_table('audit_logs', schema='crm_test')
    # ### end Alembic commands ###