from sqlalchemy import Column, String, Foreign<PERSON><PERSON>, Integer
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from app.config import settings
from sqlalchemy.dialects.postgresql import UUID


class FavouriteTeams(Base):
    __tablename__ = "favourite_teams"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.user.id"),
        primary_key=True,
    )
    teamId = Column(Integer, ForeignKey("wyscout.team_info2.teamId"), primary_key=True)

    # Relationships
    user = relationship("User")
    team_info = relationship(
        "TeamInfo",
        viewonly=True,
        primaryjoin="foreign(FavouriteTeams.teamId)==remote(TeamInfo.teamId)",
    )