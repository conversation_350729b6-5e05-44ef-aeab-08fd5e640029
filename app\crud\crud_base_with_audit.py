from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
from sqlalchemy.orm import Session
from fastapi import Request
from fastapi.encoders import jsonable_encoder

from app.crud.crud_base import CRUDBase, ModelType, CreateSchemaType, UpdateSchemaType
from app.models.user import User
from app.utils.audit_logging import log_create_event, log_update_event, log_delete_event, compare_and_log_changes


class CRUDBaseWithAudit(CRUDBase[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    CRUD base class with automatic audit logging.
    Extends the standard CRUD base to include audit trail functionality.
    """
    
    def __init__(self, model: Type[ModelType], record_type: str):
        """
        Initialize with model and record type for audit logging.
        
        Args:
            model: SQLAlchemy model class
            record_type: String identifier for the record type (e.g., "player", "activity")
        """
        super().__init__(model)
        self.record_type = record_type

    def create_with_user_and_audit(
        self, 
        db: Session, 
        *, 
        obj_in: CreateSchemaType, 
        user: User,
        request: Optional[Request] = None,
        details: Optional[str] = None
    ) -> ModelType:
        """Create record with user context and audit logging"""
        # Create the record
        db_obj = self.create_with_user(db=db, obj_in=obj_in, user=user)
        
        # Log the creation
        try:
            record_data = jsonable_encoder(db_obj)
            log_create_event(
                db=db,
                user=user,
                record_type=self.record_type,
                record_id=db_obj.id,
                record_data=record_data,
                details=details,
                request=request
            )
        except Exception as e:
            # Don't fail the operation if audit logging fails
            print(f"Audit logging failed for create operation: {e}")
        
        return db_obj

    def update_with_audit(
        self,
        db: Session,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]],
        user: User,
        request: Optional[Request] = None,
        details: Optional[str] = None,
        exclude_fields: Optional[List[str]] = None
    ) -> ModelType:
        """Update record with audit logging"""
        # Store original data for comparison
        original_data = jsonable_encoder(db_obj)
        
        # Perform the update
        updated_obj = self.update(db=db, db_obj=db_obj, obj_in=obj_in)
        
        # Log the changes
        try:
            if isinstance(obj_in, dict):
                update_data = obj_in
            else:
                # Use exclude_none=True and exclude_unset=True to only get explicitly provided fields
                update_data = obj_in.dict(exclude_none=True, exclude_unset=True)

            compare_and_log_changes(
                db=db,
                user=user,
                record_type=self.record_type,
                record_id=db_obj.id,
                old_obj=type('MockObj', (), original_data)(),  # Create mock object with original data
                new_data=update_data,
                exclude_fields=exclude_fields or ['last_updated', 'updated_at'],
                request=request
            )
        except Exception as e:
            # Don't fail the operation if audit logging fails
            print(f"Audit logging failed for update operation: {e}")
        
        return updated_obj

    def remove_with_audit(
        self, 
        db: Session, 
        *, 
        id: Any, 
        user: User,
        request: Optional[Request] = None,
        details: Optional[str] = None
    ) -> ModelType:
        """Remove record with audit logging"""
        # Get the record before deletion
        db_obj = self.get(db=db, id=id)
        if not db_obj:
            raise ValueError(f"Record with id {id} not found")
        
        # Store record data for audit log
        record_data = jsonable_encoder(db_obj)
        
        # Perform the deletion
        deleted_obj = self.remove(db=db, id=id)
        
        # Log the deletion
        try:
            log_delete_event(
                db=db,
                user=user,
                record_type=self.record_type,
                record_id=id,
                record_data=record_data,
                details=details,
                request=request
            )
        except Exception as e:
            # Don't fail the operation if audit logging fails
            print(f"Audit logging failed for delete operation: {e}")
        
        return deleted_obj

    def bulk_create_with_audit(
        self,
        db: Session,
        *,
        objs_in: List[CreateSchemaType],
        user: User,
        request: Optional[Request] = None,
        details: Optional[str] = None
    ) -> List[ModelType]:
        """Bulk create records with audit logging"""
        import uuid as uuid_lib
        
        batch_id = uuid_lib.uuid4()
        created_objects = []
        
        for obj_in in objs_in:
            # Create individual record
            db_obj = self.create_with_user(db=db, obj_in=obj_in, user=user)
            created_objects.append(db_obj)
            
            # Log creation with batch ID
            try:
                record_data = jsonable_encoder(db_obj)
                log_create_event(
                    db=db,
                    user=user,
                    record_type=self.record_type,
                    record_id=db_obj.id,
                    record_data=record_data,
                    details=f"Bulk create operation. {details}" if details else "Bulk create operation",
                    batch_id=batch_id,
                    request=request
                )
            except Exception as e:
                print(f"Audit logging failed for bulk create operation: {e}")
        
        return created_objects

    def get_with_audit_history(
        self,
        db: Session,
        *,
        id: Any,
        include_history: bool = False,
        history_limit: int = 10
    ) -> Dict[str, Any]:
        """Get record with optional audit history"""
        from app.crud.crud_audit_log import audit_log
        
        # Get the main record
        record = self.get(db=db, id=id)
        if not record:
            return None
        
        result = {"record": record}
        
        if include_history:
            # Get audit history for this record
            history = audit_log.get_record_history(
                db=db,
                record_id=str(id),
                record_type=self.record_type,
                limit=history_limit
            )
            result["audit_history"] = history
        
        return result
