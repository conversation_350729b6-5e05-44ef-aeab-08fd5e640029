"""Staging migrations

Revision ID: 4887410692ff
Revises: 48c2a9a47e31
Create Date: 2025-09-04 13:04:11.310127

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4887410692ff'
down_revision = '48c2a9a47e31'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('organization_tags',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('organization_id', sa.UUID(), nullable=False),
    sa.Column('type_of_tags', sa.String(), nullable=False),
    sa.Column('tags', postgresql.ARRAY(sa.String()), nullable=False),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_dev.organizations.id'], ),
    sa.<PERSON>KeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_organization_tags_organization_id'), 'organization_tags', ['organization_id'], unique=False, schema='crm_dev')
    op.create_table('audit_logs',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('edit_at', sa.DateTime(), nullable=False),
    sa.Column('action_type', sa.String(), nullable=False),
    sa.Column('record_type', sa.String(), nullable=False),
    sa.Column('record_id', sa.UUID(), nullable=False),
    sa.Column('record_name', sa.String(), nullable=True),
    sa.Column('previous', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('updated', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('edit_by', sa.UUID(), nullable=False),
    sa.Column('organization_id', sa.UUID(), nullable=False),
    sa.Column('ip_address', sa.String(), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('batch_id', sa.UUID(), nullable=True),
    sa.Column('details', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['edit_by'], ['crm_dev.user.id'], ),
    sa.ForeignKeyConstraint(['organization_id'], ['crm_dev.organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_audit_logs_action_type'), 'audit_logs', ['action_type'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_audit_logs_batch_id'), 'audit_logs', ['batch_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_audit_logs_edit_at'), 'audit_logs', ['edit_at'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_audit_logs_edit_by'), 'audit_logs', ['edit_by'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_audit_logs_organization_id'), 'audit_logs', ['organization_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_audit_logs_record_id'), 'audit_logs', ['record_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_audit_logs_record_type'), 'audit_logs', ['record_type'], unique=False, schema='crm_dev')
    op.create_table('favourite_teams',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('teamId', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['crm_dev.user.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'teamId'),
    schema='crm_dev'
    )
    op.create_table('priority_players',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('player_record_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['player_record_id'], ['crm_dev.player_records.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_id'], ['crm_dev.user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'player_record_id', name='_user_player_priority_uc'),
    schema='crm_dev'
    )
    op.create_index(op.f('ix_crm_dev_priority_players_id'), 'priority_players', ['id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_priority_players_player_record_id'), 'priority_players', ['player_record_id'], unique=False, schema='crm_dev')
    op.create_index(op.f('ix_crm_dev_priority_players_user_id'), 'priority_players', ['user_id'], unique=False, schema='crm_dev')
    op.alter_column('activity', 'date_deal_created',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.Date(),
               existing_nullable=True,
               schema='crm_dev')
    op.add_column('player_records', sa.Column('firstName', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('player_records', sa.Column('lastName', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('player_records', sa.Column('birthDate', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('player_records', sa.Column('weight', sa.Integer(), nullable=True), schema='crm_dev')
    op.add_column('player_records', sa.Column('height', sa.Integer(), nullable=True), schema='crm_dev')
    op.add_column('player_records', sa.Column('foot', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('player_records', sa.Column('currentTeamId', sa.Integer(), nullable=True), schema='crm_dev')
    op.add_column('player_records', sa.Column('agency', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('player_records', sa.Column('contract_expiry', sa.String(), nullable=True), schema='crm_dev')
    op.add_column('player_records', sa.Column('passportArea_id', sa.Integer(), nullable=True), schema='crm_dev')
    op.add_column('player_records', sa.Column('tags', postgresql.ARRAY(sa.String()), nullable=True), schema='crm_dev')


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('player_records', 'tags', schema='crm_dev')
    op.drop_column('player_records', 'passportArea_id', schema='crm_dev')
    op.drop_column('player_records', 'contract_expiry', schema='crm_dev')
    op.drop_column('player_records', 'agency', schema='crm_dev')
    op.drop_column('player_records', 'currentTeamId', schema='crm_dev')
    op.drop_column('player_records', 'foot', schema='crm_dev')
    op.drop_column('player_records', 'height', schema='crm_dev')
    op.drop_column('player_records', 'weight', schema='crm_dev')
    op.drop_column('player_records', 'birthDate', schema='crm_dev')
    op.drop_column('player_records', 'lastName', schema='crm_dev')
    op.drop_column('player_records', 'firstName', schema='crm_dev')
    op.drop_constraint(None, 'field_players', schema='crm_dev', type_='foreignkey')
    op.alter_column('activity', 'date_deal_created',
               existing_type=sa.Date(),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True,
               schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_priority_players_user_id'), table_name='priority_players', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_priority_players_player_record_id'), table_name='priority_players', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_priority_players_id'), table_name='priority_players', schema='crm_dev')
    op.drop_table('priority_players', schema='crm_dev')
    op.drop_table('favourite_teams', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_audit_logs_record_type'), table_name='audit_logs', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_audit_logs_record_id'), table_name='audit_logs', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_audit_logs_organization_id'), table_name='audit_logs', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_audit_logs_edit_by'), table_name='audit_logs', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_audit_logs_edit_at'), table_name='audit_logs', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_audit_logs_batch_id'), table_name='audit_logs', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_audit_logs_action_type'), table_name='audit_logs', schema='crm_dev')
    op.drop_table('audit_logs', schema='crm_dev')
    op.drop_index(op.f('ix_crm_dev_organization_tags_organization_id'), table_name='organization_tags', schema='crm_dev')
    op.drop_table('organization_tags', schema='crm_dev')
    # ### end Alembic commands ###