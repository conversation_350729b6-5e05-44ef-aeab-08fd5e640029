from app.crud.crud_base import CreateSchemaType, ModelType
from app.crud.crud_base import CRUDBase
from app import models
from typing import List, Union, Optional
from typing import Any, Dict, List, Type, TypeVar, Union
from fastapi.encoders import jsonable_encoder
from sqlalchemy.orm import Session, Load, lazyload, selectinload
from app.utils import compare_version_of_objects
from app.models import User, SourceToRecord
from app.models.team_info import TeamInfo
from app.schemas.enums import Position, TransferPeriod
from app import models
import uuid
import time
import logging
from app.db.base_class import Base
from pydantic import BaseModel
from app.utils.matching_helpers import player_to_team_segment_map
from app.config import settings
from sqlalchemy.sql import text
from app.utils import status_requests

# Set up logger for CRUD performance monitoring
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

ModelType = TypeVar("ModelType", bound=Base)
ChangeType = TypeVar("ChangeType", bound=Base)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDTeamRequest(CRUDBase):
    def create_with_user(
        self, db: Session, *, obj_in: CreateSchemaType, user: User
    ) -> ModelType:
        obj_in_data = jsonable_encoder(obj_in)
        source_in_data = None
        if "source_to_record" in obj_in_data:
            source_in_data = obj_in_data.pop("source_to_record")
        if "emails" in obj_in_data:
            obj_in_data.pop("emails")

        db_obj = self.model(
            **obj_in_data,
            created_by=user.id,
            organization_id=user.organization_id,
        )
        if source_in_data:
            ll_sources = [
                models.SourceToRecord(source_id=sor, id=uuid.uuid4())
                for sor in source_in_data
            ]
            db_obj.source_to_record = ll_sources
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_all_with_filters(
        self, db: Session, org_id: str, can_access_sensitive: bool, filters: dict
    ) -> List[ModelType]:
        start_time = time.time()
        logger.info(f"[CRUD_PERF] Starting get_all_with_filters for org {org_id}, filters: {filters}")

        # Base query construction timing
        query_build_start = time.time()
        query = (
            db.query(self.model)
            .options(
                selectinload(self.model.organization),
                selectinload(self.model.tracked_transfer),
                selectinload(self.model.source_to_record),
                selectinload(self.model.team_info),
                selectinload(self.model.creator),
            )
            .filter(self.model.organization_id == org_id)
        )
        query_build_duration = time.time() - query_build_start
        logger.info(f"[CRUD_PERF] Base query construction completed in {query_build_duration:.3f}s")

        # Apply filters timing
        filter_start = time.time()
        # Apply sensitive data filter
        if not can_access_sensitive:
            query = query.filter(self.model.is_sensitive != True)

        if "active" in filters and filters["active"] is not None:
            if filters["active"]:
                query = query.filter(self.model.status.in_(status_requests))
            else:
                query = query.filter(self.model.status == "closed")

        # Order by last_updated
        query = query.order_by(self.model.last_updated.desc())
        filter_duration = time.time() - filter_start
        logger.info(f"[CRUD_PERF] Filter application completed in {filter_duration:.3f}s")

        # Execute query timing
        execute_start = time.time()
        result = query.all()
        execute_duration = time.time() - execute_start
        logger.info(f"[CRUD_PERF] Query execution completed in {execute_duration:.3f}s, returned {len(result)} records")

        total_duration = time.time() - start_time
        logger.info(f"[CRUD_PERF] get_all_with_filters total time: {total_duration:.3f}s")

        return result

    def get_all_for_team(
        self, db: Session, org_id: str, can_access_sensitive: bool, teamId: str
    ) -> List[ModelType]:
        # Base query
        query = (
            db.query(self.model)
            .options(
                Load(self.model).selectinload("*"),
                lazyload("*"),
            )
            .filter(self.model.organization_id == org_id, self.model.teamId == teamId)
        )

        # Order by last_updated
        query = query.order_by(self.model.last_updated.desc())

        return query.all()

    def bulk_create_with_user(
        self, db: Session, *, objs_in: List[CreateSchemaType], user: User
    ) -> ModelType:
        objects_with_user = []
        for obj_in in objs_in:
            obj_in_data = jsonable_encoder(obj_in)

            source_in_data = None
            if "source_to_record" in obj_in_data:
                source_in_data = obj_in_data.pop("source_to_record")
            if "emails" in obj_in_data:
                obj_in_data.pop("emails")

            db_obj = self.model(
                **obj_in_data,
                created_by=user.id,
                organization_id=user.organization_id,
            )

            if source_in_data:
                ll_sources = [
                    models.SourceToRecord(source_id=sor, id=uuid.uuid4())
                    for sor in source_in_data
                ]
                db_obj.source_to_record = ll_sources

            objects_with_user.append(db_obj)
        db.add_all(objects_with_user)
        db.commit()
        for db_obj in objects_with_user:
            db.refresh(db_obj)
            _ = db_obj.team_info
        return objects_with_user

    def update(
        self,
        db: Session,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]],
    ) -> ModelType:
        """
        Custom update method that properly handles source_to_record relationship.
        """
        obj_in_data = jsonable_encoder(obj_in)
        source_in_data = None

        # Handle source_to_record relationship separately
        if "source_to_record" in obj_in_data:
            source_in_data = obj_in_data.pop("source_to_record")

        # Update the main object fields
        for field, value in obj_in_data.items():
            if hasattr(db_obj, field) and value is not None:
                setattr(db_obj, field, value)

        # Handle source_to_record relationship
        if source_in_data is not None:
            if source_in_data:  # If there are source IDs
                ll_sources = [
                    models.SourceToRecord(
                        source_id=sor,
                        id=uuid.uuid4(),
                        team_request_id=db_obj.id
                    )
                    for sor in source_in_data
                ]
                db_obj.source_to_record = ll_sources
            else:  # If source_to_record is empty list, clear the relationship
                db_obj.source_to_record = []

        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)

        # Ensure relationships are loaded for response serialization
        if db_obj.source_to_record:
            for source_record in db_obj.source_to_record:
                _ = source_record.source  # Force load the source relationship

        return db_obj

    def update_with_changelog(
        self,
        db: Session,
        *,
        record_id: str,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]],
        change_model: Type[ChangeType],
        record_type: str,
        user: User,
    ) -> ModelType:
        obj_in_data = jsonable_encoder(obj_in)
        source_in_data = []
        if obj_in_data["source_to_record"]:
            source_in_data = obj_in_data.pop("source_to_record")
        else:
            obj_in_data.pop("source_to_record")

        changes = compare_version_of_objects(
            db_obj,
            obj_in_data,
            user.id,
        )
        if source_in_data:
            ll_sources = [
                SourceToRecord(source_id=sor, id=uuid.uuid4()) for sor in source_in_data
            ]
            db_obj.source_to_record = ll_sources
        for field, value in obj_in_data.items():
            if value is not None:
                setattr(db_obj, field, value)
        db.add(db_obj)
        for c in changes:
            c[f"{record_type}_id"] = record_id
            db.add(change_model(**c))
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_specific(
        self,
        db: Session,
        segment: int,
        positions: List[Position],
        periods: List[TransferPeriod],
        organization_id: str,
    ) -> Optional[ModelType]:
        segment_band = player_to_team_segment_map(segment)
        return (
            db.query(self.model)
            .join(TeamInfo)
            .options(Load(self.model).selectinload("*"), lazyload("*"))
            .filter(
                self.model.position.in_(positions),
                self.model.transfer_period.contained_by(periods),
                # or_(*clauses),
                TeamInfo.segment >= segment_band.upper_bound,
                TeamInfo.segment <= segment_band.lower_bound,
                self.model.organization_id == organization_id,
            )
            .all()
        )

    def get_specific_community(
        self,
        db: Session,
        segment: int,
        positions: List[Position],
        periods: List[TransferPeriod],
        organization_id: str,
    ) -> Optional[ModelType]:
        segment_band = player_to_team_segment_map(segment)
        return db.execute(
            f"""   WITH Deduplicated AS (
                SELECT
                    "teamId",
                    "position",
                    transfer_period,
                    ARRAY_AGG(DISTINCT created_by) AS created_by_array,
                    ARRAY_AGG(organization_id) AS created_by_organization,
                    COUNT(*) as count_duplicates
                FROM
                    {settings.PG_SCHEMA}.team_requests
                WHERE
                    is_community = true
                GROUP BY
                    "teamId",
                    "position",
                    transfer_period
            )
            SELECT
                DISTINCT ON (dd."teamId", dd."position", dd.transfer_period)
                tr.*,
                (SELECT ARRAY_AGG(DISTINCT u.email)
                FROM unnest(dd.created_by_array) AS user_id
                JOIN {settings.PG_SCHEMA}.user u ON u.id = user_id) as created_by_emails,
                (SELECT ARRAY_AGG(u.name)
                FROM unnest(dd.created_by_organization) AS org_id
                JOIN {settings.PG_SCHEMA}.organizations u ON u.id = org_id) as created_by_organization,
                dd.count_duplicates,
                row_to_json(ti) as team_info,
                row_to_json(u) as creator,
                row_to_json(o) as organization
            FROM
                {settings.PG_SCHEMA}.team_requests tr
            LEFT JOIN
                Deduplicated dd
            ON
                tr."teamId" = dd."teamId"
                AND tr."position" = dd."position"
                AND tr.transfer_period = dd.transfer_period
            JOIN wyscout.team_info2 ti ON ti."teamId" = tr."teamId"
            JOIN {settings.PG_SCHEMA}.user u ON u.id = tr.created_by
            JOIN {settings.PG_SCHEMA}.organizations o ON o.id = tr.organization_id
            WHERE
                tr.is_community = true
                AND tr.organization_id != '{organization_id}'
                AND ti.segment >= {segment_band.upper_bound}
                AND ti.segment <= {segment_band.lower_bound}
                AND tr.position in ({','.join([f"'{p}'" for p in positions])})
                AND NOT EXISTS (
                    SELECT 1
                    FROM {settings.PG_SCHEMA}.team_requests crt
                    WHERE
                        crt."position" = tr."position"
                        AND crt."teamId" = tr."teamId"
                        AND crt.transfer_period = tr.transfer_period
                        AND crt.organization_id = '{organization_id}'
                )
            GROUP BY
                dd."teamId", dd."position", dd.transfer_period, tr.id, ti.*, o.*, u.*, dd.count_duplicates, dd.created_by_array, dd.created_by_organization;"""
        ).all()

    def get_community(
        self, db: Session, org_id: str, can_access_sensitive: bool
    ) -> List[ModelType]:
        start_time = time.time()
        logger.info(f"[CRUD_PERF] Starting get_community for org {org_id}")

        # Execute the complex community query
        execute_start = time.time()
        result = db.execute(
            text(
                f"""WITH Deduplicated AS (
                SELECT
                    "teamId",
                    "position",
                    transfer_period,
                    ARRAY_AGG(DISTINCT created_by) AS created_by_array,
                    ARRAY_AGG(organization_id) AS created_by_organization,
                    COUNT(*) as count_duplicates
                FROM
                    {settings.PG_SCHEMA}.team_requests
                WHERE
                    is_community = true
                GROUP BY
                    "teamId",
                    "position",
                    transfer_period
            )
            SELECT
                DISTINCT ON (dd."teamId", dd."position", dd.transfer_period, tr.last_updated)
                tr.*,
            (
            SELECT ARRAY_AGG(DISTINCT jsonb_build_object(
                'agency', org.name,
                'id', usr.id,
                'has_player_in_club', EXISTS (
                SELECT 1
                FROM wyscout.{settings.PLAYER_INFO_VIEW_SYSTEM} pi
                WHERE pi.agent_id = org.agency_id
                    AND pi."currentTeamId" = tr."teamId"
                ),
                'has_propose_badge', (
                SELECT COUNT(DISTINCT cp."player_id") >= 2
                FROM {settings.PG_SCHEMA}.community_proposals cp
                WHERE cp.organization_id = org.id
                    AND cp.created_at >= NOW() - INTERVAL '10 months'
                )
                AND (
                SELECT COUNT(*) >= 2
                FROM {settings.PG_SCHEMA}.community_proposals cp2
                WHERE cp2.organization_id = org.id
                    AND cp2.created_at >= NOW() - INTERVAL '10 months'
                ),
                'has_uploaders_badge', (
                SELECT COUNT(*) >= 10
                FROM {settings.PG_SCHEMA}.team_requests trq
                WHERE trq.organization_id = org.id
                    AND trq.is_community = true
                    AND trq.created_at >= NOW() - INTERVAL '10 months'
                )
                AND (
                SELECT COUNT(DISTINCT trq2."teamId") >= 2
                FROM {settings.PG_SCHEMA}.team_requests trq2
                WHERE trq2.organization_id = org.id
                    AND trq2.is_community = true
                    AND trq2.created_at >= NOW() - INTERVAL '10 months'
                ),
                'has_all_rounder', (
                (
                    -- Propose part
                    (SELECT COUNT(DISTINCT cp."player_id")
                    FROM {settings.PG_SCHEMA}.community_proposals cp
                    WHERE cp.organization_id = org.id
                    AND cp.created_at >= NOW() - INTERVAL '10 months') >= 2
                    AND
                    (SELECT COUNT(*)
                    FROM {settings.PG_SCHEMA}.community_proposals cp2
                    WHERE cp2.organization_id = org.id
                    AND cp2.created_at >= NOW() - INTERVAL '10 months') >= 2
                )
                AND
                (
                    -- Upload part
                    (SELECT COUNT(*) 
                    FROM {settings.PG_SCHEMA}.team_requests trq
                    WHERE trq.organization_id = org.id
                    AND trq.is_community = true
                    AND trq.created_at >= NOW() - INTERVAL '10 months') >= 2
                    AND
                    (SELECT COUNT(DISTINCT trq2."teamId") 
                    FROM {settings.PG_SCHEMA}.team_requests trq2
                    WHERE trq2.organization_id = org.id
                    AND trq2.is_community = true
                    AND trq2.created_at >= NOW() - INTERVAL '10 months') >= 2
                )
                AND
                (
                    -- Feedback part
                    (
                    SELECT COUNT(*)
                    FROM {settings.PG_SCHEMA}.community_deal cd
                    WHERE cd.organization_id = org.id
                        AND cd.type = 'received'
                        AND cd.created_at >= NOW() - INTERVAL '10 months'
                        AND cd.feedback IS NOT NULL
                    ) >= 2
                    AND
                    (
                    SELECT COUNT(*)
                    FROM {settings.PG_SCHEMA}.community_deal cd_all
                    WHERE cd_all.organization_id = org.id
                        AND cd_all.type = 'received'
                        AND cd_all.created_at >= NOW() - INTERVAL '10 months'
                    ) > 0
                    AND (
                    (
                        SELECT COUNT(*)
                        FROM {settings.PG_SCHEMA}.community_deal cd
                        WHERE cd.organization_id = org.id
                        AND cd.type = 'received'
                        AND cd.created_at >= NOW() - INTERVAL '10 months'
                        AND cd.feedback IS NOT NULL
                    )::float
                    /
                    (
                        SELECT COUNT(*)
                        FROM {settings.PG_SCHEMA}.community_deal cd_all
                        WHERE cd_all.organization_id = org.id
                        AND cd_all.type = 'received'
                        AND cd_all.created_at >= NOW() - INTERVAL '10 months'
                    )
                    ) >= 0.3
                )
                )
            ))
            FROM unnest(dd.created_by_array) WITH ORDINALITY AS ua(user_id, idx)
            JOIN LATERAL (
                SELECT dd.created_by_organization[idx] AS org_id
            ) AS org_lookup ON true
            JOIN {settings.PG_SCHEMA}.user usr ON usr.id = ua.user_id
            JOIN {settings.PG_SCHEMA}.organizations org ON org.id = org_lookup.org_id
            ) AS created_by_info,
                dd.count_duplicates,
                json_build_object(
                    'teamId', ti."teamId",
                    'area_name', ti.area_name,
                    'divisionLevel', ti."divisionLevel",
                    'league_name', ti.league_name,
                    'segment', ti.segment
                ) AS team_info,
                row_to_json(u) as creator,
                row_to_json(o) as organization
            FROM
                {settings.PG_SCHEMA}.team_requests tr
            LEFT JOIN
                Deduplicated dd
            ON
                tr."teamId" = dd."teamId"
                AND tr."position" = dd."position"
                AND tr.transfer_period = dd.transfer_period
            JOIN wyscout.team_info2 ti ON ti."teamId" = tr."teamId"
            JOIN {settings.PG_SCHEMA}.user u ON u.id = tr.created_by
            JOIN {settings.PG_SCHEMA}.organizations o ON o.id = tr.organization_id
            WHERE
                tr.is_community = true
                AND tr.organization_id != '{org_id}'
                AND tr.status != 'closed'
                AND NOT EXISTS (
                    SELECT 1
                    FROM {settings.PG_SCHEMA}.team_requests crt
                    WHERE
                        crt."position" = tr."position"
                        AND crt."teamId" = tr."teamId"
                        AND crt.transfer_period = tr.transfer_period
                        AND crt.organization_id = '{org_id}'
                )
            GROUP BY
                dd."teamId", dd."position", dd.transfer_period, tr.id, ti."teamId", ti.area_name, ti."divisionLevel", ti.league_name, ti.segment, o.*, u.*, dd.count_duplicates, dd.created_by_array, dd.created_by_organization
            ORDER BY
                tr.last_updated DESC;"""
            )
        ).all()

        execute_duration = time.time() - execute_start
        total_duration = time.time() - start_time
        logger.info(f"[CRUD_PERF] get_community query execution completed in {execute_duration:.3f}s, returned {len(result)} records")
        logger.info(f"[CRUD_PERF] get_community total time: {total_duration:.3f}s")

        return result

    def get_orgs_with_com_requests(self, db: Session) -> List[str]:
        return db.execute(
            f"""
            select o.id::text from {settings.PG_SCHEMA}.organizations o 
            join {settings.PG_SCHEMA}.purchases p on p.organization_id = o.id 
            join {settings.PG_SCHEMA}.modules m on m.id = p.module_id 
            where m."name" = 'community_requests'"""
        ).all()


team_request = CRUDTeamRequest(models.TeamRequest)
