import uuid
from typing import Optional
from pydantic import BaseModel
from app.schemas.user import UserShort
from app.schemas.team_info import TeamInfo


class FavouriteTeamCreate(BaseModel):
    teamId: int


class FavouriteTeamRead(BaseModel):
    user_id: uuid.UUID
    teamId: int
    user: Optional[UserShort]
    team_info: Optional[TeamInfo]

    class Config:
        orm_mode = True


class FavouriteTeamUpdate(BaseModel):
    # No fields to update for this simple model
    pass


class FavouriteTeamDelete(BaseModel):
    teamId: int
