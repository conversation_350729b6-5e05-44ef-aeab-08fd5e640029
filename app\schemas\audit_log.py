from datetime import datetime
import uuid
from typing import Any, Optional, Dict
from pydantic import BaseModel, Field

from app.schemas.user import UserShort
from app.schemas.enums import ActionType

class AuditLogBase(BaseModel):
    action_type: ActionType
    record_type: str
    record_id: uuid.UUID
    record_name: Optional[str] = None
    previous: Optional[Dict[str, Any]] = None
    updated: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    batch_id: Optional[uuid.UUID] = None
    details: Optional[str] = None


class AuditLogCreate(AuditLogBase):
    pass


class AuditLogUpdate(AuditLogBase):
    action_type: Optional[ActionType] = None
    record_type: Optional[str] = None
    record_id: Optional[uuid.UUID] = None


class AuditLog(AuditLogBase):
    id: uuid.UUID
    edit_at: datetime
    edit_by: uuid.UUID
    organization_id: uuid.UUID
    editor: Optional[UserShort] = None

    class Config:
        orm_mode = True


class AuditLogFilter(BaseModel):
    """Filter parameters for audit log queries"""
    action_type: Optional[ActionType] = None
    record_type: Optional[str] = None
    record_id: Optional[uuid.UUID] = None
    edit_by: Optional[uuid.UUID] = None
    organization_id: Optional[uuid.UUID] = None
    batch_id: Optional[uuid.UUID] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    search: Optional[str] = None  # Search in record_name, details


class AuditLogResponse(BaseModel):
    """Response model for paginated audit log results"""
    items: list[AuditLog]
    total: int
    page: int
    size: int
    pages: int
