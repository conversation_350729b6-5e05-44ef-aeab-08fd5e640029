from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.community_deal import CommunityDealCreate, CommunityDealUpdate
from sqlalchemy.orm import Session, lazyload, Load
from typing import Optional, TypeVar, TypeVar, Any
from app.db.base_class import Base
from pydantic import BaseModel
from app.models import User, CommunityProposal

ModelType = TypeVar("ModelType", bound=Base)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)
ChangeType = TypeVar("ChangeType", bound=Base)


class CRUDCommunityDeal(CRUDBase[models.CommunityDeal, CommunityDealCreate, CommunityDealUpdate]):
    def get_oposite_deal(self, db: Session, deal_id: Any) -> Optional[ModelType]:
        return db.query(self.model).options(Load(self.model).selectinload("*"),
                lazyload("*"),
        ).filter(self.model.community_deal_id == deal_id).first()
    
    def create_double_deal(self, db: Session, proposal_obj: CommunityProposal, user:User, request_creator, suitability_score, phone_number:str = None) -> Optional[ModelType]:
        player_info_view_crm = proposal_obj.player_records.player_info_view_crm
        team_info = proposal_obj.team_requests.team_info
        name = player_info_view_crm.shortName
        if not name:
            name = f"{player_info_view_crm.firstName} {player_info_view_crm.lastName}"
        receiver_deal = self.model(community_proposal_id=proposal_obj.id,
        title=f"Received: {name} ({player_info_view_crm.team_name}) for {team_info.name} {'/'.join([ el.upper() for el in proposal_obj.position])}",
        type='received', created_by=request_creator.id, organization_id=request_creator.organization_id, suitability_score=suitability_score)
        db.add(receiver_deal)
        db.commit()
        db.refresh(receiver_deal)
        
        sender_deal = self.model(community_proposal_id=proposal_obj.id,
        title=f"Sent: {name} for {team_info.area_name}/{team_info.league_name} {'/'.join([el.upper() for el in proposal_obj.position])}",
        type='proposed', created_by=user.id, organization_id=user.organization_id, 
        organization_proposed_to=request_creator.organization.name, community_deal_id=receiver_deal.id, suitability_score=suitability_score,
        phone_number=phone_number)
        db.add(sender_deal)
        db.commit()

        db.refresh(sender_deal)

        return {'received': receiver_deal, 'proposed': sender_deal}

community_deal = CRUDCommunityDeal(models.CommunityDeal)