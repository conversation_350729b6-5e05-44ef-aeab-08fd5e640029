from pydantic import BaseModel, Field
from typing import Optional
from datetime import date

class Team(BaseModel):
    teamId: int
    officialName: str
    area_name: str
    divisionLevel: int = Field(None, alias="divisionLevel")
    league_name: Optional[str]
    category: str

    class Config:
        orm_mode = True
        use_cache=True
    
class PlayerFromTeam(BaseModel):
    playerId: str
    firstName: str
    lastName: str
    birthDate: Optional[str]
    passport: Optional[str]
    height: Optional[str]
    weight: Optional[str]
    primary_ws_position: Optional[str]
    secondary_ws_position: Optional[str]
    current_value: Optional[int]
    contract_expiry: Optional[date]
    agent: Optional[str]
    mine: Optional[bool]
    player_url: Optional[str]
    is_favourite: Optional[bool]

    class Config:
        orm_mode = True
        use_cache=True