from pydantic import BaseSettings, root_validator
from typing import Optional, List, Set


class CommonSettings(BaseSettings):
    APP_NAME: str = "Shadow Eleven Backend"
    PROD: bool
    TEST: bool
    STAGING: Optional[bool]


class ServerSettings(BaseSettings):
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    MAINTENANCE_MODE: bool = False


class DatabaseSettings(BaseSettings):
    REALM_APP_ID: str
    DB_URL_PROD: str
    DB_URL_DEV: Optional[str]
    DB_URL: Optional[str]
    POSTGRES_PROD_STR: str


class SQLSettings(BaseSettings):
    PG_BASE_NAME: str = "wyscout_raw_production"
    PG_URL_PROD: str
    PG_URL_TEST: Optional[str]
    PG_URL_DEV: Optional[str]
    PG_URL: Optional[str]
    PG_URL_ASYNC: Optional[str]
    PG_SCHEMA_PROD: str = "crm"
    PG_SCHEMA_DEV: str = "crm_dev"
    PG_SCHEMA_TEST: str = "crm_test"
    PG_SCHEMA: Optional[str]


class AuthSettings(BaseSettings):
    JWT_SECRET_KEY: str
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24
    SECURE_COOKIE: bool = False


class MatchingSettings(BaseSettings):
    MATCHING_API_USR: str
    MATCHING_API_PASS: str
    MATCHING_API_URL_PROD: str
    MATCHING_API_URL_DEV: str = "http://localhost:6962"
    MATCHING_API_URL_STAGING: str = "https://shadchan-staging-aolebn4toq-ew.a.run.app/"
    MATCHING_API_URL: Optional[str]


class AgentSettings(BaseSettings):
    AGENT_API_USR: str
    AGENT_API_PASS: str
    AGENT_API_URL_PROD: str
    AGENT_API_URL_DEV: str = "http://localhost:8000/networkdata"
    AGENT_API_URL: Optional[str]


class RankAPISettings(BaseSettings):
    RANKS_API_USR: str
    RANKS_API_PASS: str
    RANK_API_URL: str


class StripeAPISettings(BaseSettings):
    STRIPE_CLIENT_URL: str
    STRIPE_API_USER: str
    STRIPE_API_KEY: str


class LookupSettings(BaseSettings):
    LOOKUP_API_USR: str
    LOOKUP_API_PASS: str
    LOOKUP_API_URL: str


class MailSettings(BaseSettings):
    OUTLOOK_ENSKAI_ALERTS_PASS: str
    OUTLOOK_ENSKAI_ALERTS_ADDRESS: str
    SENDGRID_API_KEY: str


class FrontEndSettings(BaseSettings):
    FRONTEND_URL_PROD: Optional[str]
    FRONTEND_URL_DEV: Optional[str]
    FRONTEND_URL_STAGING: Optional[str]
    FRONTEND_URL_TESTING: Optional[str]


class DocUrls(BaseSettings):
    DOCS_URL: Optional[str]
    REDOC_URL: Optional[str]


class WyscoutCreds(BaseSettings):
    WYSCOUT_USR: str
    WYSCOUT_PASS: str


class ResourcesSettings(BaseSettings):
    SENSITIVE_RESOURCES: List[str] = ["Contract", "ContractUpload"]
    SENSITIVE_MODULES: List[str] = [
        "legal",
        "financial",
        "power_bi_embed",
        "dashboards",
        "community_requests",
    ]
    ADMIN_EMAILS: str
    PLAYER_SENSITIVE_FIELDS: Set[str] = {
        "xtransfer_low",
        "xtransfer_high",
        "xgross_salary_high",
        "xtransfer_next_high",
        "xtransfer_next_low",
        "xgross_salary_next_high",
        "xgross_salary_next_low",
        "rec_max_investment",
    }
    TEAM_REQUEST_SENSITIVE_FIELDS: Set[str] = set("is_sensitive")
    PLAYER_VIEWERS_FILTER_FIELDS: Set[str] = {
        "player_features",
        "quality",
        "potential",
        "club_asking_price",
        "deals_with_player",
        "phone_number",
        "current_gross_salary",
        "changelog",
        "created_at",
        "created_by",
        "creator",
        "scouting_reports",
        "is_priority",
        "proactively_scouted",
        "organization_id",
        "organization",
        "is_sensitive",
        "source_to_record",
    }
    TEAM_REQUEST_VIEWERS_FILTER_FIELDS: Set[str] = {
        "proposed_players",
        "changelog",
        "created_at",
        "created_by",
        "reason_for_outcome",
        "organization_id",
        "organization",
        "creator",
    }


class CloudStorageSettings(BaseSettings):
    GOOGLE_APPLICATION_CREDENTIALS: str = "creds.json"
    CLOUD_STORAGE_BUCKET_NAME: str
    CAPTCHA_SECRET: str


class CachingSettings(BaseSettings):
    REDIS_HOST: str = "redis://************"
    CACHE_PREFIX: Optional[str]
    DEFAULT_TTL: int = 60 * 60 * 0.5


class PdfGenerationSettings(BaseSettings):
    # GTK_FOLDER: str
    pass

class DataArchitecture(BaseSettings):
    PLAYER_INFO_VIEW_CRM: str = "player_info_view_crm"
    PLAYER_INFO_VIEW_SYSTEM: str = "player_info_view_system"


class BaseConfig(BaseSettings):
    # Can be set to 'MasterUser' or 'ServicePrincipal'
    AUTHENTICATION_MODE = "MasterUser"
    # Workspace Id in which the report is present
    WORKSPACE_ID: str
    # Report Id for which Embed token needs to be generated
    REPORT_ID: str
    # Id of the Azure tenant in which AAD app and Power BI report is hosted. Required only for ServicePrincipal authentication mode.
    TENANT_ID: str
    # Client Id (Application Id) of the AAD app
    CLIENT_ID: str
    # Client Secret (App Secret) of the AAD app. Required only for ServicePrincipal authentication mode.
    CLIENT_SECRET: str
    # Scope Base of AAD app. Use the below configuration to use all the permissions provided in the AAD app through Azure portal.
    SCOPE_BASE = ["https://analysis.windows.net/powerbi/api/.default"]
    # URL used for initiating authorization request
    AUTHORITY_URL = "https://login.microsoftonline.com/organizations"
    # Master user email address. Required only for MasterUser authentication mode.
    POWER_BI_USER: Optional[str]
    # Master user email password. Required only for MasterUser authentication mode.
    POWER_BI_PASS: Optional[str]


class FireBaseSettings(BaseSettings):
    FIREBASE_API_KEY_PROD: str
    FIREBASE_API_KEY_STAGING: str
    FIREBASE_API_KEY_TESTING: str


class HUBSPOT_API_KEY(BaseSettings):
    HUBSPOT_API_KEY: str


class TWILLIO_API(BaseSettings):
    TWILIO_AUTH_TOKEN: str


class WhatsAppBotSettings(BaseSettings):
    WHATSAPP_BOT_API_KEY: str


class GeminiSettings(BaseSettings):
    GEMINI_API_KEY: str


class Settings(
    CommonSettings,
    ServerSettings,
    DatabaseSettings,
    SQLSettings,
    AuthSettings,
    MatchingSettings,
    AgentSettings,
    LookupSettings,
    MailSettings,
    FrontEndSettings,
    DocUrls,
    RankAPISettings,
    ResourcesSettings,
    CloudStorageSettings,
    CachingSettings,
    WyscoutCreds,
    BaseConfig,
    FireBaseSettings,
    StripeAPISettings,
    PdfGenerationSettings,
    HUBSPOT_API_KEY,
    TWILLIO_API,
    WhatsAppBotSettings,
    GeminiSettings,
    DataArchitecture,
):
    class Config:
        env_file = ".env"

    @root_validator(allow_reuse=True)
    def get_prod_vs_dev(cls, values):
        values["FIREBASE_API_URL"] = (
            f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken"
        )
        values["FIREBASE_EMAIL_API_URL"] = (
            f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword"
        )
        values["FIRABASE_MESSAGING_URL"] = (
            f"https://fcm.googleapis.com/v1/projects/footballanalytics/messages:send"
        )
        values["REFRESH_TOKEN_URL"] = f"https://securetoken.googleapis.com/v1/token"
        values["DB_URL"] = (
            values["DB_URL_PROD"] if values["PROD"] else values["DB_URL_DEV"]
        )
        values["ADMIN_EMAILS"] = values["ADMIN_EMAILS"].split(",")

        values["MATCHING_API_URL"] = (
            values["MATCHING_API_URL_PROD"]
            if values["PROD"]
            else values["MATCHING_API_URL_DEV"]
        )

        values["AGENT_API_URL"] = (
            values["AGENT_API_URL_PROD"]
            if values["PROD"]
            else values["AGENT_API_URL_DEV"]
        )

        values["PG_URL"] = (
            values["PG_URL_DEV"]
            if values["TEST"]
            else values["PG_URL_PROD"] if values["PROD"] else values["PG_URL_DEV"]
        )

        values["PG_SCHEMA"] = (
            values["PG_SCHEMA_PROD"] if values["PROD"] else values["PG_SCHEMA_DEV"]
        )
        values["PG_URL_ASYNC"] = values["PG_URL"].replace(
            "postgresql://", "postgresql+asyncpg://"
        )

        values["DOCS_URL"] = None if values["PROD"] else "/docs"
        values["CACHE_PREFIX"] = "prod_" if values["PROD"] else "dev_"

        values["FIREBASE_API_KEY"] = values["FIREBASE_API_KEY_PROD"]
        values["FIREBASE_CREDS"] = "creds.json"
        values["PROJECT_ID"] = "footballanalytics"
        values["TARGET_AUDIENCE"] = "footballanalytics"

        if values["STAGING"]:
            values["PLAYER_INFO_VIEW_CRM"] = "player_info_view_dev"
            values["PG_SCHEMA"] = values["PG_SCHEMA_DEV"]
            values["DOCS_URL"] = "/docs"
            values["MATCHING_API_URL"] = values["MATCHING_API_URL_STAGING"]
            values["CACHE_PREFIX"] = "staging_"
            values["FIREBASE_API_KEY"] = values["FIREBASE_API_KEY_STAGING"]
            values["FIREBASE_CREDS"] = "creds_staging.json"
            values["PROJECT_ID"] = "shadow-eleven-staging"
            values["TARGET_AUDIENCE"] = "shadow-eleven-staging-49931"

        if values["TEST"]:
            values["PLAYER_INFO_VIEW_CRM"] = "player_info_view_test"
            values["PG_SCHEMA"] = values["PG_SCHEMA_TEST"]
            values["DOCS_URL"] = "/docs"
            values["MATCHING_API_URL"] = values["MATCHING_API_URL_STAGING"]
            values["CACHE_PREFIX"] = "test_"
            values["FIREBASE_API_KEY"] = values["FIREBASE_API_KEY_TESTING"]
            values["FIREBASE_CREDS"] = "creds_testing.json"
            values["PROJECT_ID"] = "shadow-eleven-testing"
            values["TARGET_AUDIENCE"] = "shadow-eleven-testing"

        return values


settings = Settings()
