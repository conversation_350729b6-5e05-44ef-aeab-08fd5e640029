from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session
from datetime import datetime, timedelta, timezone
from app.schemas.request_field import (
    RequestField,
    RequestFieldCreate,
    RequestFieldUpdate,
    RequestFieldViewAll,
    RequestFieldShare,
)
from app.schemas.field_requests import (
    FieldRequests,
    FieldRequestsCreate,
    FieldRequestsUpdate,
)
from app.schemas.share_create_token import ShareTokenCreate
from app import crud, models
from app.api import deps, utils
from app.utils.sharing.create_share_token import create_share_token
from app.utils import get_url_frontend
from app.utils.mail import format_number
from app.utils import contries_dict
from app.utils.generate_shadow_pdf import generate_request_pdf
from fastapi.responses import StreamingResponse
from app.utils.audit_decorator import audit_action
from urllib.parse import quote
from io import BytesIO
from copy import deepcopy

router = APIRouter()
from fastapi import Response


@router.get("/all", response_model=List[RequestFieldViewAll])
def read_request_fields(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve request_fields.
    """
    utils.check_module_access(current_user, "shadow_squads")
    utils.check_get_all(RequestField, current_user)
    return crud.request_field.get_all_fields(db, current_user.organization_id)


@router.post("/", response_model=RequestField)
@audit_action(action_type="create", record_type="request_field")
async def create_request_field(
    *,
    db: Session = Depends(deps.get_db),
    obj_in: RequestFieldCreate,
    request: Request,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new request_field.
    """
    utils.check_module_access(current_user, "shadow_squads")
    utils.check_create(RequestField, current_user)

    request_field = crud.request_field.create_with_user(
        db=db,
        obj_in=obj_in,
        user=current_user,
    )

    return request_field


@router.post("/add_request/", response_model=FieldRequests)
async def add_request(
    *,
    db: Session = Depends(deps.get_db),
    obj_in: FieldRequestsCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Add a request to a field.
    """
    field_request = crud.request_in_the_field.create(
        db=db,
        obj_in=obj_in,
    )

    return field_request


@router.put("/refresh_field/", response_model=RequestField)
@audit_action(
    action_type="update",
    record_type="request_field",
    get_original_object=lambda kwargs: crud.request_field.get(
        kwargs["db"], kwargs["obj_in"].id
    ),
)
async def refresh_field(
    *,
    db: Session = Depends(deps.get_db),
    obj_in: RequestFieldCreate,
    request: Request,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create or update a Request field with requests.
    """
    utils.check_module_access(current_user, "shadow_squads")
    if obj_in.id:
        field = crud.request_field.get(db=db, id=obj_in.id)
        if field:
            # Update the field using the special method
            field_update = RequestFieldUpdate(**obj_in.dict(exclude={"requests"}))
            field = crud.request_field.update_with_contact_fields(
                db=db,
                db_obj=field,
                obj_in=field_update,
            )

            # Delete existing requests on this field
            crud.request_in_the_field.delete_all_requests(db=db, field_id=field.id)
        else:
            # Create the field
            field = crud.request_field.create_with_user(
                db=db, obj_in=obj_in.dict(exclude={"requests"}), user=current_user
            )
    else:
        # Create the field
        field = crud.request_field.create_with_user(
            db=db, obj_in=obj_in.dict(exclude={"requests"}), user=current_user
        )

    # Create new requests for this field
    for request in obj_in.requests or []:
        data = request.dict()
        data["field_id"] = field.id
        crud.request_in_the_field.create(db=db, obj_in=FieldRequestsCreate(**data))
    field = crud.request_field.get(db=db, id=field.id)

    return field


@router.get("/{id}", response_model=RequestField)
def read_request_field(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get request_field by ID.
    """
    utils.check_module_access(current_user, "shadow_squads")
    utils.check_get_one(RequestField, current_user)

    request_field = crud.request_field.get_by_org(
        db=db, id=id, org_id=current_user.organization_id
    )
    if not request_field:
        raise HTTPException(
            status_code=404,
            detail=("The request_field with this id does not exist in the system"),
        )
    return request_field


@router.post("/share-tokens", response_model=str)
def create_token_request_field_endpoint(
    payload: ShareTokenCreate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    field_request = crud.request_field.get(db=db, id=payload.resource_id)
    if not field_request:
        raise HTTPException(status_code=404, detail="Request field not found.")

    if not bool(payload.expires_at):
        payload.expires_at = datetime.now(timezone.utc) + timedelta(days=365 * 100)
    token_value = create_share_token(
        db=db,
        resource_type="request_field",
        resource_id=payload.resource_id,
        expires_at=payload.expires_at,
        hide_field_view=payload.hide_field_view,
    )

    url = get_url_frontend()

    # Return the full shareable URL or just the token
    share_url = f"{url}/share/shadow-requests/{token_value}"
    return share_url


@router.get("/share/{token}", response_model=RequestFieldShare)
def get_shared_resource(token: str, db: Session = Depends(deps.get_db)):
    # 1. Lookup token in DB
    share_token = (
        db.query(models.ShareToken).filter(models.ShareToken.token == token).first()
    )
    if not share_token:
        raise HTTPException(status_code=404, detail="Share token not found.")

    if share_token.is_disabled:
        raise HTTPException(status_code=403, detail="Share token disabled.")

    if share_token.expires_at < datetime.now():
        raise HTTPException(status_code=403, detail="Share token expired.")

    # 5. Fetch the actual resource
    request_field = crud.request_field.get(db=db, id=share_token.resource_id)

    if not request_field:
        raise HTTPException(status_code=404, detail="Resource not found.")

    # Process each field request to hide team names and logos if needed
    if request_field and request_field.requests:
        for field_request in request_field.requests:
            if (
                field_request.is_team_hidden
                and field_request.request
                and field_request.request.team_info
            ):
                original_team_info = field_request.request.team_info
                hidden_team_info = deepcopy(original_team_info)
                hidden_team_info.name = ""
                field_request.request.description = ""
                hidden_team_info.imageDataURL = ""
                field_request.request.team_info = hidden_team_info

    organization_logo = crud.organization.get(
        db=db, id=request_field.organization_id
    ).agency_logo_url
    request_field.org_logo = organization_logo

    request_field.hide_field_view = getattr(share_token, "hide_field_view", False)

    return request_field


@router.put("/{id}", response_model=RequestField)
@audit_action(
    action_type="update",
    record_type="request_field",
    get_original_object=lambda kwargs: crud.request_field.get_by_org(
        kwargs['db'], kwargs['id'], kwargs['current_user'].organization_id
    )
)
async def update_request_field(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    obj_in: RequestFieldUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update an request_field.
    """
    utils.check_modify(RequestField, current_user)

    request_field = crud.request_field.get_by_org(
        db=db, id=id, org_id=current_user.organization_id
    )
    if not request_field:
        raise HTTPException(
            status_code=404,
            detail=("The request_field with this id does not exist in the system"),
        )

    request_field = crud.request_field.update(
        db=db,
        db_obj=request_field,
        obj_in=obj_in,
    )

    return request_field


@router.put("/request/{id}", response_model=FieldRequests)
async def update_field_request(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    obj_in: FieldRequestsUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a request on a field.
    """
    field_request = crud.request_in_the_field.get(db=db, id=id)

    if not field_request:
        raise HTTPException(
            status_code=404,
            detail="The field_request with this id does not exist in the system",
        )

    field_request = crud.request_in_the_field.update(
        db=db,
        db_obj=field_request,
        obj_in=obj_in,
    )

    return field_request


@router.delete("/{id}", response_model=RequestField)
@audit_action(action_type="delete", record_type="request_field",)
def delete_request_field(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    request: Request,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete an request_field.
    """
    utils.check_module_access(current_user, "shadow_squads")
    utils.check_delete(RequestField, current_user)

    request_field = crud.request_field.get_by_org(
        db=db, id=id, org_id=current_user.organization_id
    )
    if not request_field:
        raise HTTPException(
            status_code=404,
            detail=("The request_field with this id does not exist in the system"),
        )

    request_field = crud.request_field.remove(db=db, id=id)
    return request_field


@router.delete("/remove_request/{id}")
def delete_field_request(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Remove a request from a field.
    """

    field_request = crud.request_in_the_field.get(db=db, id=id)

    if not field_request:
        raise HTTPException(
            status_code=404,
            detail="The field_request with this id does not exist in the system",
        )
    field_request = crud.request_in_the_field.remove(db=db, id=id)
    return None


@router.delete("/request/bulk-delete")
async def delete_all_requests(
    *,
    db: Session = Depends(deps.get_db),
    ids: Optional[str] = None,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Removes all requests from the request field.
    """
    utils.check_module_access(current_user, "shadow_squads")
    field_requests_out = crud.request_in_the_field.delete_all_requests(
        db=db, field_id=ids
    )

    return field_requests_out


@router.get("/pdf/{field_id}", response_model=RequestField)
async def download_shadow_pdf(
    *,
    db: Session = Depends(deps.get_db),
    field_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get request_field by ID.
    """
    utils.check_module_access(current_user, "shadow_squads")
    request_field = crud.request_field.get_by_org(
        db=db, id=field_id, org_id=current_user.organization_id
    )

    # organization_logo_url = crud.organization.get_logo_by_id(db=db, organization_id=current_user.organization_id)
    requests_data = []
    try:
        field_data = {
            "name": request_field.name,
            "has_hardcoded": False,
            "contact_name": request_field.contact_name,
            "contact_email": request_field.contact_email,
            "contact_phone": request_field.contact_phone,
        }
    except:
        field_data = {
            "name": request_field.name,
            "has_hardcoded": False,
            "contact_name": request_field.contact_name,
            "contact_email": request_field.contact_email,
            "contact_phone": request_field.contact_phone,
        }
    # if organization_logo_url:
    #    field_data['has_hardcoded'] = organization_logo_url
    index_pos_set = set()
    for request in request_field.requests:
        request_info = request.request.team_info

        if not request.transfer_fee:
            request.transfer_fee = request.request.max_value
        if not request.salary:
            request.salary = request.request.max_net_salary

        dd = {
            "name": (request_info.name),
            "teamImg": request_info.imageDataURL,
            "division_level": request.request.team_info.divisionLevel,
            "transfer_fee": (
                format_number(request.transfer_fee) if request.transfer_fee else "Free"
            ),
            "salary": (format_number(request.salary) if request.salary else "N/A"),
            "area_name_text": request_info.area_name,
            "area_name": f"https://flagcdn.com/w40/{contries_dict[request_info.area_name]}.png",
            "is_eu": (
                "https://flagcdn.com/w40/eu.png"
                if request.request.eu_passport
                else None
            ),
            "x_px": f"{int(request.x_cordinate * 11.25)}px",
            "y_px": f"{int(request.y_cordinate * 11.7)}px",
            "position": request.request.position.upper(),
            "is_team_hidden": request.is_team_hidden,
        }
        index_pos_set.add(request.index_position)
        requests_data.append(dd)
    pdf_content = generate_request_pdf(requests_data, field_data)

    filename = f"{request_field.name}.pdf"
    quoted_filename = quote(filename)

    headers = {
        # Safe for non-ASCII names
        "Content-Disposition": f"attachment; filename*=UTF-8''{quoted_filename}"
    }

    return StreamingResponse(
        BytesIO(pdf_content), media_type="application/pdf", headers=headers
    )


@router.get("/download/{token}", response_model=RequestField)
def get_shared_resource_pdf(token: str, db: Session = Depends(deps.get_db)):
    # 1. Lookup token in DB
    share_token = (
        db.query(models.ShareToken).filter(models.ShareToken.token == token).first()
    )
    if not share_token:
        raise HTTPException(status_code=404, detail="Share token not found.")

    if share_token.is_disabled:
        raise HTTPException(status_code=403, detail="Share token disabled.")

    if share_token.expires_at < datetime.now():
        raise HTTPException(status_code=403, detail="Share token expired.")

    # 5. Fetch the actual resource
    request_field = crud.request_field.get(db=db, id=share_token.resource_id)

    # organization_logo_url = crud.organization.get_logo_by_id(db=db, organization_id=current_user.organization_id)
    requests_data = []
    try:
        field_data = {
            "name": request_field.name,
            "has_hardcoded": False,
            "contact_name": request_field.contact_name,
            "contact_email": request_field.contact_email,
            "contact_phone": request_field.contact_phone,
        }
    except:
        field_data = {
            "name": request_field.name,
            "has_hardcoded": False,
            "contact_name": request_field.contact_name,
            "contact_email": request_field.contact_email,
            "contact_phone": request_field.contact_phone,
        }

    # if organization_logo_url:
    #    field_data['has_hardcoded'] = organization_logo_url
    index_pos_set = set()
    for request in request_field.requests:
        request_info = request.request.team_info

        if not request.transfer_fee:
            request.transfer_fee = request.request.max_value
        if not request.salary:
            request.salary = request.request.max_net_salary

        dd = {
            "name": (request_info.name),
            "division_level": request.request.team_info.divisionLevel,
            "teamImg": request_info.imageDataURL,
            "transfer_fee": (
                format_number(request.transfer_fee) if request.transfer_fee else "Free"
            ),
            "salary": (format_number(request.salary) if request.salary else "N/A"),
            "area_name": f"https://flagcdn.com/w40/{contries_dict[request_info.area_name]}.png",
            "area_name_text": request_info.area_name,
            "is_eu": (
                "https://flagcdn.com/w40/eu.png"
                if request.request.eu_passport
                else None
            ),
            "x_px": f"{int(request.x_cordinate * 11.25)}px",
            "y_px": f"{int(request.y_cordinate * 11.7)}px",
            "position": request.request.position,
            "is_team_hidden": request.is_team_hidden,
        }
        index_pos_set.add(request.index_position)
        requests_data.append(dd)
    pdf_content = generate_request_pdf(requests_data, field_data)

    filename = f"{request_field.name}.pdf"
    quoted_filename = quote(filename)

    headers = {
        # Safe for non-ASCII names
        "Content-Disposition": f"attachment; filename*=UTF-8''{quoted_filename}"
    }

    return StreamingResponse(
        BytesIO(pdf_content), media_type="application/pdf", headers=headers
    )
