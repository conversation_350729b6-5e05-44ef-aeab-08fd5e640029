import uuid
from typing import Optional, List, TYPE_CHECKING
from datetime import date
from app.schemas.enums import ContractType
from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
)
from app.schemas.team_info import TeamInfo
from app.schemas.contact import Contact
from app.schemas.contract_upload import ContractUpload
from app.schemas.player_info_view_crm import PlayerInfoViewCrmShort
if TYPE_CHECKING:
    from app.schemas.staff_record import StaffRecordShort

class ContractUpdate(ExtendedUpdateBase):
    start_date: Optional[date]
    end_date: Optional[date]
    active_status: Optional[bool]
    currency: Optional[str]
    player_id: Optional[uuid.UUID]
    staff_id: Optional[uuid.UUID]
    contract_type: Optional[ContractType]
    agent_id: Optional[uuid.UUID]
    agent_alt_name: Optional[str]
    teamId: Optional[int]
    exclusive: Optional[bool]
    termination_fee: Optional[float]
    pct_commission_agreed: Optional[float]
    registered_in: Optional[str]
    gross_salary: Optional[float]
    signing_fee: Optional[float]
    goal_bonus: Optional[float]
    assist_bonus: Optional[float]
    matches_played_bonus: Optional[float]
    minimum_fee_release_clause: Optional[float]
    option_years: Optional[int]
    installments: Optional[List[dict]]
    coverage: Optional[List[str]]
    notify: Optional[List[str]]
    company: Optional[str]

    class Config:
        use_cache=True
        orm_mode = True



class ContractCreate(ContractUpdate, ExtendedCreateBase):
    start_date: date
    end_date: date
    active_status: bool
    currency: Optional[str]
    player_id: Optional[uuid.UUID]
    staff_id: Optional[uuid.UUID]
    contract_type: ContractType
    agent_id: Optional[uuid.UUID]
    agent_alt_name: Optional[str]
    teamId: Optional[int]
    exclusive: Optional[bool]
    termination_fee: Optional[float]
    pct_commission_agreed: Optional[float]
    registered_in: Optional[str]
    gross_salary: Optional[float]
    signing_fee: Optional[float]
    goal_bonus: Optional[float]
    assist_bonus: Optional[float]
    matches_played_bonus: Optional[float]
    minimum_fee_release_clause: Optional[float]
    option_years: Optional[int]
    installments: Optional[List[dict]]
    coverage: Optional[List[str]]
    notify: Optional[List[str]]
    company: Optional[str]

class ContractShort(ContractCreate, ExtendedBase):
    staff: Optional["StaffRecordShort"]  
    player: Optional["PlayerInfoViewCrmShort"]
    team_info: Optional[TeamInfo]
    agent_on_contract: Optional[Contact]
    player_id: Optional[uuid.UUID]
    staff_id: Optional[uuid.UUID]


class Contract(ContractShort):
    uploads: Optional[List[ContractUpload]]



ContractUpload.update_forward_refs(Contract=ContractShort)