"""date_deal_created to date in the model

Revision ID: 38a455d849f1
Revises: 7087deea4f5d
Create Date: 2025-08-25 16:42:45.113770

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '38a455d849f1'
down_revision = '7087deea4f5d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('activity', 'date_deal_created',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.Date(),
               existing_nullable=True,
               schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('activity', 'date_deal_created',
               existing_type=sa.Date(),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True,
               schema='crm_test')
    # ### end Alembic commands ###