
from sqlalchemy import (
    <PERSON>um<PERSON>,
    String,
    Integer,
    Date,
    Float,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ey,
    JSON,
)
from sqlalchemy.orm import relationship, declared_attr
from sqlalchemy.dialects.postgresql import UUID, ARRAY

from app.models.extended_base_mixin import ExtendedBaseMixin
from app.db.base_class import Base
from app.config import settings


class Contract(Base, ExtendedBaseMixin):
    __tablename__ = "contracts"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    player_id = Column(
        UUID(as_uuid=True),
        ForeignKey(
            f"{settings.PG_SCHEMA}.player_records.id",
            ondelete="SET NULL",
        ),
        index=True,
    )
    player = relationship("PlayerInfoViewCrm",
                          primaryjoin="foreign(Contract.player_id)==remote(PlayerInfoViewCrm.id)")

    staff_id = Column(
        UUID(as_uuid=True),
        ForeignKey(
            f"{settings.PG_SCHEMA}.staff_records.id",
            ondelete="SET NULL",
        ),
        index=True,
    )
    staff = relationship("StaffRecord")
    start_date = Column(Date, index=True)
    end_date = Column(Date, index=True)
    active_status = Column(Boolean, index=True)
    currency = Column(String(10))
    contract_type = Column(String(50))
    notes = Column(String)
    uploads = relationship("ContractUpload", back_populates="contract")
    notify = Column(ARRAY(String))

    __mapper_args__ = {
        "polymorphic_identity": "contract",
        "polymorphic_on": contract_type,
    }


class HasAgent:
    @declared_attr
    def agent_id(cls):
        return cls.__table__.c.get(
            "agent_id",
            Column(
                UUID(as_uuid=True),
                ForeignKey(f"{settings.PG_SCHEMA}.contacts.id", ondelete="SET NULL"),
                index=True,
            ),
        )

    @declared_attr
    def agent_alt_name(cls):
        return cls.__table__.c.get("agent_alt_name", Column(String))

    @declared_attr
    def agent_on_contract(cls):
        return cls.__table__.c.get("agent_on_contract", relationship("Contact"))


class HasTeam:
    @declared_attr
    def teamId(cls):
        return cls.__table__.c.get(
            "teamId",
            Column(
                Integer,
                ForeignKey("wyscout.team_info2.teamId"),
                index=True,
            ),
        )

    @declared_attr
    def team_info(cls):
        return cls.__table__.c.get("team_info2", relationship("TeamInfo", viewonly=True, primaryjoin='foreign(ClubContract.teamId)==remote(TeamInfo.teamId)' ))


class Mandate(HasAgent, Contract):
    coverage = Column(ARRAY(String))
    __mapper_args__ = {
        "polymorphic_identity": "mandate",
    }

class Commercial(Contract):
    company = Column(String)
    __mapper_args__ = {
        "polymorphic_identity": "commercial",
    }


class RepresentationAgreement(HasAgent, Contract):
    exclusive = Column(Boolean)
    termination_fee = Column(Float)
    pct_commission_agreed = Column(Float)
    registered_in = Column(String(50))

    __mapper_args__ = {
        "polymorphic_identity": "representation_agreement",
    }


class ClubContract(HasTeam, Contract):
    gross_salary = Column(JSON)
    signing_fee = Column(Float)
    goal_bonus = Column(Float)
    assist_bonus = Column(Float)
    matches_played_bonus = Column(Float)
    minimum_fee_release_clause = Column(Float)
    option_years = Column(Integer)
    __mapper_args__ = {
        "polymorphic_identity": "club_contract",
    }


class CommissionAgreement(HasTeam, HasAgent, Contract):
    installments = Column(JSON)
    __mapper_args__ = {
        "polymorphic_identity": "commission_agreement",
    }


class ContractUpload(Base, ExtendedBaseMixin):
    __tablename__ = "contract_uploads"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    id = Column(String, primary_key=True, )
    contract_id = Column(
        UUID(as_uuid=True), ForeignKey(f"{settings.PG_SCHEMA}.contracts.id"), index=True
    )
    contract = relationship("Contract", back_populates="uploads")
    name = Column(String)