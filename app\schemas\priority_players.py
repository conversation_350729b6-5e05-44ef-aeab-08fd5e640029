import uuid
from typing import Optional
from pydantic import BaseModel
from app.schemas.user import UserShort
from app.schemas.player_record import PlayerRecordShort


class PriorityPlayerCreate(BaseModel):
    player_record_id: uuid.UUID


class PriorityPlayerRead(BaseModel):
    id: uuid.UUID
    user_id: uuid.UUID
    player_record_id: uuid.UUID
    user: Optional[UserShort]
    player_record: Optional[PlayerRecordShort]

    class Config:
        orm_mode = True


class PriorityPlayerUpdate(BaseModel):
    # No fields to update for this simple model
    pass


class PriorityPlayerDelete(BaseModel):
    player_record_id: uuid.UUID
