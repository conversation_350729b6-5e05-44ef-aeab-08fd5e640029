import json
import or<PERSON><PERSON>
from typing import Any, Optional, List
import requests
from fastapi import API<PERSON><PERSON><PERSON>, Depends, BackgroundTasks, HTTPException, Request
from sqlalchemy.orm import Session
from app.api.endpoints.platform_notifications import create_in_app_notification
from app.api.endpoints.notifications import update_notifications
from app.schemas.staff_record import (
    <PERSON><PERSON><PERSON><PERSON>,
    StaffRecordCreate,
    StaffRecordUpdate,
)
from app import crud, models
from app.api import deps, utils
from app.api.endpoints.contacts import read_contact
from app.utils import mail
from app.api.endpoints.message_subscription import read_subscriptions
from fastapi.params import Query
from app.utils.audit_decorator import audit_action
router = APIRouter()


@router.get("/", response_model=List[StaffRecord])
def read_staff_records(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve staffs.
    """
    #ToDo - limit staff record for different roles.
    utils.check_get_all(<PERSON><PERSON><PERSON><PERSON>, current_user)
    exclude = utils.exclude_based_on_role(current_user)
    can_access_sense = utils.can_access_sensitive(current_user)
    org_id = current_user.organization_id

    staff = crud.staff_record.get_all_w_org(db, org_id, can_access_sense)

    if exclude:
        return_staff = [StaffRecord.from_orm(x) for x in staff if exclude(x, current_user)]
    else:
        return_staff = [StaffRecord.from_orm(x) for x in staff]

    return return_staff

@router.get("/{id}",)
def read_staff_record(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
        """
        Get StaffRecord by ID.
        """
        #ToDo - limit staff record for different roles.
        staff_record = crud.staff_record.get_by_org(db=db, id=id, org_id=current_user.organization_id)
        utils.check_get_one(staff_record, current_user)
        return StaffRecord.from_orm(staff_record)


@router.post("/", response_model=StaffRecord)
@audit_action(action_type="create", record_type="staff_record")
async def create_staff_record(
    *,
    db: Session = Depends(deps.get_db),
    staff_record_in: StaffRecordCreate,
    #background_tasks: BackgroundTasks,
    request: Request,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create a new Staff Record.
    """

    #ToDo - limit staff record for different roles.
    utils.check_create(StaffRecord, current_user)

    existing_staff = crud.staff_record.get_staff_with_transfermarkt(
        db=db, tmId=staff_record_in.staff_id, org_id=current_user.organization_id
    )
    if existing_staff:
        raise HTTPException(
            status_code=409, detail="staff_already_exists"
        )
    role = crud.staff_record.get_staff_role(db=db, tmId=staff_record_in.staff_id)['role']
    staff_record_in.roles = [role]
    staff_record = crud.staff_record.create_with_user_assigne(
        db=db,
        obj_in=staff_record_in,
        user=current_user,
    )
    #for assigned_id in staff_record_in.assigned_to_record:
    #    pass
        #ToDo - add staff email notifications
        #background_tasks.add_task(
        #    mail.send_mail_new_player,
        #    staff_record,
        #    current_user=current_user,
        #    db=db,
        #    read_contact_func=read_contact,
        #    read_subscription_func=read_subscriptions,
        #    create_in_app_notification=create_in_app_notification,
        #    assigned_id=assigned_id,
        #)
    return staff_record

@router.put("/{id}", response_model=StaffRecord)
@audit_action(
    action_type="update",
    record_type="staff_record",
    get_original_object=lambda kwargs: crud.staff_record.get_by_org(
        kwargs['db'], kwargs['id'], kwargs['current_user'].organization_id
    )
)
def update_staff(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    staff_record_in: StaffRecordUpdate,
    request: Request,
    background_tasks: BackgroundTasks,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a staff record.
    """
    staff = crud.staff_record.get_by_org(db=db, id=id, org_id=current_user.organization_id)

    #ToDo - limit staff record for different roles.
    utils.check_modify(staff, current_user)
    staff = crud.staff_record.update_with_assigned_to(db=db, db_obj=staff, obj_in=staff_record_in)
    
    print(staff_record_in.assigned_to_record)
    for assigned_id in staff_record_in.assigned_to_record:
        background_tasks.add_task(
            mail.send_mail_new_staff,
            staff=staff,
            current_user=current_user,
            db=db,
            read_contact_func=read_contact,
            read_subscription_func=read_subscriptions,
            assigned_id=assigned_id,
        )
    
    return staff

@router.delete("/bulk-delete", response_model=List[StaffRecord])
@audit_action(action_type="delete", record_type="staff_record",)
async def delete_multiple_staffs(
    *,
    db: Session = Depends(deps.get_db),
    ids: Optional[str] = None,
    request: Request,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete multiple staff records.
    """
    if ids is None:
        raise HTTPException(status_code=400, detail="No ids provided")

    ids_list = ids.split(',')
    staffs_ou = []
    for id in ids_list:
        staff_record = crud.staff_record.get_by_org(db=db, id=id, org_id=current_user.organization_id)
        utils.check_delete(staff_record, current_user)
        staff_out = StaffRecord.from_orm(staff_record)
        staffs_ou.append(staff_out)
        crud.staff_record.remove(db=db, id=id)
    return staffs_ou

@router.get("/history/{staff_id}", response_model=List[dict])
def read_staff_record_history(
    *,
    db: Session = Depends(deps.get_db),
    staff_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get Transfermarkt history by transfermarkt staff id.
    """
    return crud.staff_record.get_all_w_org_history(db, staff_id)