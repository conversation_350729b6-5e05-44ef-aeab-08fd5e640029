from sqlalchemy import Column, Integer, Table
from app.db.session import engine
from app.db.base_class import Base


class PlayerInfoViewSystem(Base):
    __table__ = Table(
        "player_info_view_system",
        Base.metadata,
        Column("playerId", Integer, primary_key=True),
        extend_existing=True,
        autoload_with=engine,
        schema="wyscout",
        info=dict(is_view=True)
    )
