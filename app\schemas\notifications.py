import uuid
from typing import Optional, List
from pydantic import BaseModel
from app.schemas.user import UserShort

class NotificationsUpdate(BaseModel):
    id: uuid.UUID
    user_id: uuid.UUID
    player_id: uuid.UUID
    player_notifications: Optional[bool]

    class Config:
        orm_mode = True

class NotificationsRead(BaseModel):
    id: Optional[uuid.UUID]
    user_id: Optional[uuid.UUID]
    player_id: Optional[uuid.UUID]
    player_notifications: Optional[bool]

    class Config:
        orm_mode = True


class NotificationsCreate(NotificationsUpdate):
    id: Optional[uuid.UUID]

class Notifications(NotificationsUpdate):
    user: UserShort

    class Config:
        orm_mode = True