"""Added priority players and priority teams + new data arch columns to player record

Revision ID: 8c9f417a57fe
Revises: 34578446c448
Create Date: 2025-08-12 11:54:50.124487

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8c9f417a57fe'
down_revision = '34578446c448'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('favourite_teams',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=True),
    sa.Column('teamId', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['crm_test.user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_favourite_teams_id'), 'favourite_teams', ['id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_favourite_teams_teamId'), 'favourite_teams', ['teamId'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_favourite_teams_user_id'), 'favourite_teams', ['user_id'], unique=False, schema='crm_test')
    op.create_table('priority_players',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('player_record_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['player_record_id'], ['crm_test.player_records.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['crm_test.user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'player_record_id', name='_user_player_priority_uc'),
    schema='crm_test'
    )
    op.create_index(op.f('ix_crm_test_priority_players_id'), 'priority_players', ['id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_priority_players_player_record_id'), 'priority_players', ['player_record_id'], unique=False, schema='crm_test')
    op.create_index(op.f('ix_crm_test_priority_players_user_id'), 'priority_players', ['user_id'], unique=False, schema='crm_test')
    op.alter_column('player_records', 'firstName',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('player_records', 'lastName',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('player_records', 'birthDate',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('player_records', 'foot',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('player_records', 'default_position',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('player_records', 'agency',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('player_records', 'contract_expiry',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True,
               schema='crm_test')
    op.drop_column('player_records', 'is_priority', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('player_records', sa.Column('is_priority', sa.BOOLEAN(), autoincrement=False, nullable=True), schema='crm_test')
    op.alter_column('player_records', 'contract_expiry',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('player_records', 'agency',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('player_records', 'default_position',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('player_records', 'foot',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('player_records', 'birthDate',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('player_records', 'lastName',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True,
               schema='crm_test')
    op.alter_column('player_records', 'firstName',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True,
               schema='crm_test')
    op.drop_index(op.f('ix_crm_test_priority_players_user_id'), table_name='priority_players', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_priority_players_player_record_id'), table_name='priority_players', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_priority_players_id'), table_name='priority_players', schema='crm_test')
    op.drop_table('priority_players', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_favourite_teams_user_id'), table_name='favourite_teams', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_favourite_teams_teamId'), table_name='favourite_teams', schema='crm_test')
    op.drop_index(op.f('ix_crm_test_favourite_teams_id'), table_name='favourite_teams', schema='crm_test')
    op.drop_table('favourite_teams', schema='crm_test')
    # ### end Alembic commands ###