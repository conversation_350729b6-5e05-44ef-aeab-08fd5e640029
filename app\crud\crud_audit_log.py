from typing import Any, Dict, List, Optional, Union
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func
from fastapi.encoders import jsonable_encoder

from app.crud.crud_base import CRUDBase
from app.models.audit_log import AuditLog
from app.models.user import User
from app.schemas.audit_log import AuditLogCreate, AuditLogUpdate, AuditLogFilter


class CRUDAuditLog(CRUDBase[AuditLog, AuditLogCreate, AuditLogUpdate]):

    def create_with_user(
        self,
        db: Session,
        *,
        obj_in: AuditLogCreate,
        user: User,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> AuditLog:
        """Create audit log entry with user context"""
        obj_in_data = jsonable_encoder(obj_in)

        # Override or set ip_address and user_agent if provided
        if ip_address is not None:
            obj_in_data["ip_address"] = ip_address
        if user_agent is not None:
            obj_in_data["user_agent"] = user_agent

        db_obj = self.model(
            **obj_in_data, edit_by=user.id, organization_id=user.organization_id
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_paginated(
        self,
        db: Session,
        *,
        filters: AuditLogFilter,
        skip: int = 0,
        limit: int = 100,
        organization_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Get paginated audit logs with filters"""
        query = db.query(self.model)

        # Organization filter
        if organization_id:
            query = query.filter(self.model.organization_id == organization_id)

        # Apply filters
        if filters.action_type:
            query = query.filter(self.model.action_type == filters.action_type)

        if filters.record_type:
            query = query.filter(self.model.record_type == filters.record_type)

        if filters.record_id:
            query = query.filter(self.model.record_id == filters.record_id)

        if filters.edit_by:
            query = query.filter(self.model.edit_by == filters.edit_by)

        if filters.batch_id:
            query = query.filter(self.model.batch_id == filters.batch_id)

        if filters.start_date:
            query = query.filter(self.model.edit_at >= filters.start_date)

        if filters.end_date:
            query = query.filter(self.model.edit_at <= filters.end_date)

        if filters.search:
            search_term = f"%{filters.search}%"
            query = query.filter(
                or_(
                    self.model.record_name.ilike(search_term),
                    self.model.details.ilike(search_term),
                )
            )

        # Get total count
        total = query.count()

        # Apply pagination and ordering
        items = query.order_by(desc(self.model.edit_at)).offset(skip).limit(limit).all()

        return {
            "items": items,
            "total": total,
            "page": (skip // limit) + 1 if limit > 0 else 1,
            "size": limit,
            "pages": (total + limit - 1) // limit if limit > 0 else 1,
        }

    def get_record_history(
        self,
        db: Session,
        *,
        record_id: str,
        record_type: str,
        organization_id: Optional[str] = None,
        limit: int = 50,
    ) -> List[AuditLog]:
        """Get audit history for a specific record"""
        query = db.query(self.model).filter(
            and_(
                self.model.record_id == record_id, self.model.record_type == record_type
            )
        )

        if organization_id:
            query = query.filter(self.model.organization_id == organization_id)

        return query.order_by(desc(self.model.edit_at)).limit(limit).all()

    def get_user_activity(
        self,
        db: Session,
        *,
        user_id: str,
        organization_id: Optional[str] = None,
        days: int = 30,
        limit: int = 100,
    ) -> List[AuditLog]:
        """Get recent activity for a specific user"""
        from datetime import datetime, timedelta

        start_date = datetime.now() - timedelta(days=days)
        query = db.query(self.model).filter(
            and_(self.model.edit_by == user_id, self.model.edit_at >= start_date)
        )

        if organization_id:
            query = query.filter(self.model.organization_id == organization_id)

        return query.order_by(desc(self.model.edit_at)).limit(limit).all()

    def get_statistics(
        self, db: Session, *, organization_id: Optional[str] = None, days: int = 30
    ) -> Dict[str, Any]:
        """Get audit log statistics"""
        from datetime import datetime, timedelta

        start_date = datetime.now() - timedelta(days=days)
        query = db.query(self.model).filter(self.model.edit_at >= start_date)

        if organization_id:
            query = query.filter(self.model.organization_id == organization_id)

        # Action type statistics
        action_stats = (
            query.with_entities(
                self.model.action_type, func.count(self.model.id).label("count")
            )
            .group_by(self.model.action_type)
            .all()
        )

        # Record type statistics
        record_stats = (
            query.with_entities(
                self.model.record_type, func.count(self.model.id).label("count")
            )
            .group_by(self.model.record_type)
            .all()
        )

        # User activity statistics
        user_stats = (
            query.with_entities(
                self.model.edit_by, func.count(self.model.id).label("count")
            )
            .group_by(self.model.edit_by)
            .order_by(desc(func.count(self.model.id)))
            .limit(10)
            .all()
        )

        return {
            "total_actions": query.count(),
            "action_types": {action: count for action, count in action_stats},
            "record_types": {record: count for record, count in record_stats},
            "top_users": [
                {"user_id": str(user), "count": count} for user, count in user_stats
            ],
        }


audit_log = CRUDAuditLog(AuditLog)
