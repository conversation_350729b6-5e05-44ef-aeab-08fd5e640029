from typing import List, Optional
from sqlalchemy.orm import Session, selectinload
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException

from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.priority_players import PriorityPlayerCreate, PriorityPlayerRead, PriorityPlayerUpdate


class CRUDPriorityPlayers(CRUDBase[models.PriorityPlayers, PriorityPlayerCreate, PriorityPlayerUpdate]):
    
    def create_with_user(
        self, db: Session, *, obj_in: PriorityPlayerCreate, user_id: str
    ) -> models.PriorityPlayers:
        """Create a priority player for a specific user"""
        try:
            db_obj = self.model(
                user_id=user_id,
                player_record_id=obj_in.player_record_id
            )
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
            return db_obj
        except IntegrityError:
            db.rollback()
            raise HTTPException(
                status_code=409, 
                detail="Player is already marked as priority for this user"
            )
    
    def get_by_user(
        self, db: Session, user_id: str
    ) -> List[models.PriorityPlayers]:
        """Get all priority players for a specific user"""
        return (
            db.query(self.model)
            .options(
                selectinload(self.model.user),
                selectinload(self.model.player_record).selectinload(
                    models.PlayerRecord.player_info
                )
            )
            .filter(self.model.user_id == user_id)
            .all()
        )
    
    def delete_by_user_and_player(
        self, db: Session, user_id: str, player_record_id: str
    ) -> bool:
        """Delete a priority player for a specific user"""
        db_obj = (
            db.query(self.model)
            .filter(
                self.model.user_id == user_id,
                self.model.player_record_id == player_record_id
            )
            .first()
        )
        if db_obj:
            db.delete(db_obj)
            db.commit()
            return True
        return False
    
    def is_priority_for_user(
        self, db: Session, user_id: str, player_record_id: str
    ) -> bool:
        """Check if a player is marked as priority for a specific user"""
        return (
            db.query(self.model)
            .filter(
                self.model.user_id == user_id,
                self.model.player_record_id == player_record_id
            )
            .first()
        ) is not None


priority_players = CRUDPriorityPlayers(models.PriorityPlayers)
