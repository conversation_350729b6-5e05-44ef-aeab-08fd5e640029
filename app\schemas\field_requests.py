import uuid
from typing import Optional, TYPE_CHECKING
from pydantic import BaseModel
from app.schemas.team_request import TeamRequest

from app.schemas.request_field import RequestField


class FieldRequestsUpdate(BaseModel):
    field_id: Optional[uuid.UUID]
    request_id: Optional[uuid.UUID]
    index_position: Optional[int]
    transfer_fee: Optional[float]
    salary: Optional[float]
    x_cordinate: Optional[float]
    y_cordinate: Optional[float]
    is_team_hidden: Optional[bool] = False
    class Config:
        use_cache=True
        orm_mode = True


class FieldRequestsCreate(FieldRequestsUpdate):
    request_id: uuid.UUID
    x_cordinate: float
    y_cordinate: float
    transfer_fee: Optional[float]
    salary: Optional[float]
    is_team_hidden: Optional[bool] = False

class FieldRequests(BaseModel):
    id: uuid.UUID
    request_id: uuid.UUID
    request: TeamRequest
    x_cordinate: Optional[float]
    y_cordinate: Optional[float]
    transfer_fee: Optional[float]
    salary: Optional[float]
    is_team_hidden: Optional[bool] = False


    class Config:
        orm_mode = True
        use_cache=True
