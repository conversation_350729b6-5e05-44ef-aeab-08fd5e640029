from sqlalchemy import Column, <PERSON>, Integer, DateTime, Foreign<PERSON>ey, JSON, Date
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from app.models.comment import Comment

from app.models.extended_base_mixin import ExtendedBaseMixin
from app.db.base_class import Base
from app.config import settings


class Activity(Base, ExtendedBaseMixin):
    __tablename__ = "activity"
    __table_args__ = {"schema": settings.PG_SCHEMA}

    playerId = Column(
        Integer,
        index=True,
    )
    player_info_view_crm = relationship(
        "PlayerInfoViewCrm",
        viewonly=True,
        primaryjoin="and_(foreign(Activity.playerId)==remote(PlayerInfoViewCrm.playerId), foreign(Activity.organization_id)==remote(PlayerInfoViewCrm.organization_id))",
    )
    teamId = Column(
        Integer,
        ForeignKey("wyscout.team_info2.teamId"),
        index=True,
    )
    team_info = relationship(
        "TeamInfo",
        viewonly=True,
        primaryjoin="foreign(Activity.teamId)==remote(TeamInfo.teamId)",
    )
    staff_id = Column(
        Integer,
        ForeignKey("transfermarkt.staff_info.staff_id"),
        index=True,
    )
    staff_info = relationship(
        "StaffInfo",
        viewonly=True,
        primaryjoin="foreign(Activity.staff_id)==remote(StaffInfo.staff_id)",
    )
    assigned_to_record = relationship(
        "AssignedToRecord",
        primaryjoin="Activity.id==AssignedToRecord.activity_id",
        cascade="all, delete-orphan",
    )
    stage = Column(String)
    staff = Column(String)
    title = Column(String)
    next_action = Column(Date)

    updated_by_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.contacts.id"),
        index=True,
    )
    updated_by = relationship(
        "Contact", back_populates="updated_activities", foreign_keys=[updated_by_id]
    )
    due_date = Column(Date)
    date_deal_created = Column(Date)
    type = Column(String)

    comments = relationship(
        "Comment",
        back_populates="acitvity",
        foreign_keys=Comment.activity_id,
    )
    description = Column(String)
    type = Column(String)
