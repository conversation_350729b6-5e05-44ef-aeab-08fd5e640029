from sqlalchemy import (
    <PERSON>umn,
    <PERSON><PERSON><PERSON>,
    Integer,
    String,
    Float
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.config import settings


class RankOutput(Base):
    __tablename__ = "rank_outputs"
    __table_args__ = {"schema": settings.PG_SCHEMA}

    id = Column(UUID(as_uuid=True), ForeignKey(f"{settings.PG_SCHEMA}.rank_records.id"), primary_key=True)
    playerId = Column(
        Integer,
    )
    player_rating = Column(Float)
    player_rank = Column(Integer)
    feature_json = Column(String, default=r'{}')
    # TODO add relationships to transfers, injuries etc. to replicate the output from rank app download
    player_agg_stats = relationship(
        "PlayerAggregateStats",
        viewonly=True, 
        primaryjoin='foreign(RankOutput.playerId)==remote(PlayerAggregateStats.playerId)',
        # secondary='join(RankOutput, PlayerAggregateStats, RankOutput.playerId == PlayerAggregateStats.playerId)'
        
    )
    rank_record = relationship(
        "RankRecord",
        viewonly=True, 
        primaryjoin='foreign(RankOutput.id)==remote(RankRecord.id)',
        # secondary='join(RankOutput, PlayerAggregateStats, RankOutput.playerId == PlayerAggregateStats.playerId)'
        
    )