from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session
import pandas as pd
from datetime import datetime, timedelta, timezone
from app.schemas.football_field import (
    FootballField,
    FootballFieldCreate,
    FootballFieldUpdate,
    FootballFieldViewAll,
    FootballFieldShare
)
from app.schemas.field_players import (
    FieldPlayers,
    FieldPlayersCreate,
    FieldPlayersUpdate,
    FieldPlayersAutofill
)
from app.schemas.share_create_token import (
    ShareTokenCreate
)
from app import crud, models
from app.api import deps, utils
import itertools
from app.utils.generate_shadow_pdf import generate_pdf
from app.utils import contries_dict, pdf_logos
from app.utils.mail import format_number
from app.config import settings
from app.utils.sharing.create_share_token import create_share_token
from app.utils import get_url_frontend
from app.utils.audit_decorator import audit_action
from fastapi.responses import StreamingResponse
from urllib.parse import quote
from io import BytesIO

router = APIRouter()
from fastapi import Response

shadowSquadsPositions = {  # TODO make the squad positions formation specific
    "gk": ["gk"],
    "defenders_center": ["cb", "lcb", "rcb"],
    "defenders_wing": ["lb", "rb"],
    "midfielders": ["cam", "dmc", "cmf"],
    "attackers": ["st", "ss"],
    "wingers": ["lw", "rw"],
}

reverse_pos_mapping = {
    "gk": ["gk"],
    "lb": ["lb", "lb5", "lwb"],
    "cb": ["cb"],
    "lcb": ["lcb", "lcb3"],
    "rcb": ["rcb", "rcb3"],
    "rb": ["rb", "rb5", "rwb"],
    "cam": ["amf"],
    "dmc": ["dmc", "rdmf", "ldmf"],
    "cmf": ["lcmf", "lcmf3", "rcmf", "rcmf3"],
    "lw": ["lamf", "lw", "lwf"],
    "lamf": ["lamf", "lw", "lwf"],
    "lamf3": ["lamf", "lw", "lwf"],
    "rw": ["rwf", "rw", "ramf"],
    "ramf": ["rwf", "rw", "ramf", "ramf3"],
    "ramf3": ["rwf", "rw", "ramf", "ramf3"],
    "st": ["cf"],
    "ss": ["ss"],
}

positionMappingWS = {
    "gk": "gk",
    "amf": "cam",
    "cb": "cb",
    "cf": "st",
    "dmf": "dmc",
    "lamf": "lw",
    "lb": "lb",
    "lb5": "lb",
    "lcb": "lcb",
    "lcb3": "lcb",
    "lcmf": "cmf",
    "lcmf3": "cmf",
    "ldmf": "dmc",
    "lw": "lw",
    "lwb": "lb",
    "lwf": "lw",
    "ramf": "rw",
    "rb": "rb",
    "rb5": "rb",
    "rcb": "rcb",
    "rcb3": "rcb",
    "rcmf": "cmf",
    "rcmf3": "cmf",
    "rdmf": "dmc",
    "rw": "rw",
    "rwb": "rb",
    "rwf": "rw",
    "ss": "ss",
}

formations = {
    "442": {
        "role_grids": {
            "gk": [0],
            "defenders_left": [1],
            "defenders_center": [2, 3],
            "defenders_right": [4],
            "wingers_left": [5],
            "wingers_right": [8],
            "midfielders": [6, 7],
            "attackers": [9, 10],
        },
        "roles_needed": {
            "gk": 1,
            "defenders_center": 2,
            "defenders_left": 1,
            "defenders_right": 1,
            "midfielders": 2,
            "attackers": 2,
            "wingers_left": 1,
            "wingers_right": 1,
        },
        "position_mapping": {
            "gk": ["gk"],
            "defenders_center": ["cb"],
            "defenders_left": ["lb"],
            "defenders_right": ["rb"],
            "midfielders": ["cam", "dmc", "cmf"],
            "attackers": ["st", "ss"],
            "wingers_left": ["lw"],
            "wingers_right": ["rw"],
        },
    },
    "4231": {
        "role_grids": {
            "gk": [0],
            "defenders_left": [1],
            "defenders_center": [2, 3],
            "defenders_right": [4],
            "wingers_left": [7],
            "wingers_right": [9],
            "midfielders": [5, 6, 8],
            "attackers": [10],
        },
        "roles_needed": {
            "gk": 1,
            "defenders_center": 2,
            "defenders_left": 1,
            "defenders_right": 1,
            "midfielders": 3,
            "attackers": 1,
            "wingers_left": 1,
            "wingers_right": 1,
        },
        "position_mapping": {
            "gk": ["gk"],
            "defenders_center": ["cb"],
            "defenders_left": ["lb"],
            "defenders_right": ["rb"],
            "midfielders": ["cam", "dmc", "cmf"],
            "attackers": ["st", "ss"],
            "wingers_left": ["lw"],
            "wingers_right": ["rw"],
        },
    },
    "532": {
        "role_grids": {
            "gk": [0],
            "defenders_left": [1],
            "defenders_right": [5],
            "defenders_center": [2, 3, 4],
            "wingers_left": [6],
            "wingers_right": [8],
            "midfielders": [7],
            "attackers": [9, 10],
        },
        "roles_needed": {
            "gk": 1,
            "defenders_center": 3,
            "defenders_left": 1,
            "defenders_right": 1,
            "midfielders": 1,
            "attackers": 2,
            "wingers_left": 1,
            "wingers_right": 1,
        },
        "position_mapping": {
            "gk": ["gk"],
            "defenders_center": ["cb"],
            "defenders_left": ["lb"],
            "defenders_right": ["rb"],
            "midfielders": ["cam", "dmc", "cmf"],
            "attackers": ["st", "ss"],
            "wingers_left": ["lw", "lamf", "lamf3"],
            "wingers_right": ["rw", "ramf", "ramf3"],
        },
    },
    "433": {
        "role_grids": {
            "gk": [0],
            "defenders_left": [1],
            "defenders_right": [4],
            "defenders_center": [2, 3],
            "wingers_left": [5],
            "wingers_right": [7],
            "midfielders": [6],
            "attackers": [8, 9, 10],
        },
        "roles_needed": {
            "gk": 1,
            "defenders_center": 2,
            "defenders_left": 1,
            "defenders_right": 1,
            "midfielders": 1,
            "attackers": 3,
            "wingers_left": 1,
            "wingers_right": 1,
        },
        "position_mapping": {
            "gk": ["gk"],
            "defenders_center": ["cb"],
            "defenders_left": ["lb"],
            "defenders_right": ["rb"],
            "midfielders": ["cam", "dmc", "cmf"],
            "attackers": ["st", "ss"],
            "wingers_left": ["lw", "lamf", "lamf3"],
            "wingers_right": ["rw", "ramf", "ramf3"],
        },
    },
    "352": {
        "role_grids": {
            "gk": [0],
            "defenders_left": [1],
            "defenders_right": [3],
            "defenders_center": [2],
            "wingers_left": [4],
            "wingers_right": [8],
            "midfielders": [5, 6, 7],
            "attackers": [9, 10],
        },
        "roles_needed": {
            "gk": 1,
            "defenders_center": 1,
            "defenders_left": 1,
            "defenders_right": 1,
            "midfielders": 3,
            "attackers": 2,
            "wingers_left": 1,
            "wingers_right": 1,
        },
        "position_mapping": {
            "gk": ["gk"],
            "defenders_center": ["cb"],
            "defenders_left": ["lb"],
            "defenders_right": ["rb"],
            "midfielders": ["cam", "dmc", "cmf"],
            "attackers": ["st", "ss"],
            "wingers_left": ["lw"],
            "wingers_right": ["rw"],
        },
    },
}

coordinates = [  # Y - X
    (92, 41.5),
    (65, 6.5),
    (75, 24.0),
    (75, 59.0),
    (65, 76.5),
    (55, 24.0),
    (55, 59.0),
    (25, 6.5),
    (33, 41.5),
    (25, 76.5),
    (0, 41.5),
]

# Add a function to validate foot values
def validate_foot(foot_value):
    """Validate and normalize foot values to match the Foot enum."""
    valid_values = ["left", "right", "both"]
    
    if foot_value not in valid_values:
        return None  # Return None if the value is False
    
    return foot_value

@router.get("/all", response_model=List[FootballFieldViewAll])
def read_football_fields(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve football_fields.
    """
    utils.check_module_access(current_user, "shadow_squads")
    utils.check_get_all(FootballField, current_user)
    return crud.football_field.get_all_fields(db, current_user.organization_id)


@router.post("/", response_model=FootballField)
@audit_action(action_type="create", record_type="football_field")
async def create_football_field(
    *,
    db: Session = Depends(deps.get_db),
    obj_in: FootballFieldCreate,
    request: Request,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new football_field.
    """
    utils.check_module_access(current_user, "shadow_squads")
    utils.check_create(FootballField, current_user)

    football_field = crud.football_field.create_with_user(
        db=db,
        obj_in=obj_in,
        user=current_user,
    )

    return football_field


@router.post("/add_player/", response_model=FieldPlayers)
async def add_player(
    *,
    db: Session = Depends(deps.get_db),
    obj_in: FieldPlayersCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Add a player to a field.
    """
    field_player = crud.field_player.create_player_w_suitability(
        db=db,
        obj_in=obj_in,
    )

    return field_player

@router.put("/refresh_field/", response_model=FootballField)
@audit_action(
    action_type="update",
    record_type="football_field",
        get_original_object=lambda kwargs: crud.football_field.get_by_org(
        kwargs["db"], kwargs["obj_in"].id, kwargs["current_user"].organization_id
    ),
)
async def refresh_field(
    *,
    db: Session = Depends(deps.get_db),
    obj_in: FootballFieldCreate,
    request: Request,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create or update a football field with players.
    """
    utils.check_module_access(current_user, "shadow_squads")
    if obj_in.id:
        field = crud.football_field.get_by_org(db=db, id=obj_in.id, org_id=current_user.organization_id)
        if field:
            # Update the field using the special method
            field_update = FootballFieldUpdate(**obj_in.dict(exclude={"players"}))
            field = crud.football_field.update_with_contact_fields(
                db=db,
                db_obj=field,
                obj_in=field_update,
            )

            # Delete existing players on this field
            crud.field_player.delete_all_players(db=db, field_id=field.id)
        else:
            # Create the field
            field = crud.football_field.create_with_user(db=db, obj_in=obj_in.dict(exclude={"players"}), user=current_user)
    else:
        # Create the field
        field = crud.football_field.create_with_user(db=db, obj_in=obj_in.dict(exclude={"players"}), user=current_user)

    # Create new players for this field
    for player in obj_in.players or []:
        data = player.dict()
        data["field_id"] = field.id
        crud.field_player.create_player_w_suitability(
            db=db,
            obj_in=FieldPlayersCreate(**data)
        )
    field = crud.football_field.get_by_org(db=db, id=field.id, org_id=current_user.organization_id)

    return field


@router.get(
    "/fill_suitable_players/{formation}/{teamId}"
    , response_model=List[FieldPlayersAutofill]
)
async def fill_suitable_players(
    *,
    db: Session = Depends(deps.get_db),
    formation: str,
    teamId: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Add a player to a field.
    """
    utils.check_module_access(current_user, "shadow_squads")
    if not teamId:
        raise HTTPException(status_code=404, detail="team_not_linked")
    results = crud.suit_score_joined.get_matches_for_team(
        db, team_id=teamId, org_id=str(current_user.organization_id)
    )

    results2 = pd.DataFrame(results)
    results2 = results2.fillna(False)
    shortlisted_players = []
    for key, value in formations[formation]["position_mapping"].items():
        num_needed = formations[formation]["roles_needed"][key]
        all_pos = [reverse_pos_mapping[x] for x in value]
        all_pos = list(itertools.chain.from_iterable(all_pos))
        if results2.shape[0] > 0:
            tmp_df = (
                results2[(results2.primary_ws_position.isin(all_pos))]
                .head(num_needed + 1)
                .copy()
                .reset_index(drop=True)
            )
        else:
            tmp_df = pd.DataFrame(
                columns=[
                    "playerId",
                    "fullName",
                    "shortName",
                    "imageDataURL",
                    "primary_ws_position",
                    "secondary_ws_position",
                    "firstName",
                    "lastName",
                    "birthDate",
                    "team_name",
                    "birthArea_name",
                    "passportArea_name",
                    "eu",
                    "player_url",
                    "current_value",
                    "hiring_team_id",
                    "club_asking_price",
                    "current_gross_salary",
                    "rating",
                    "hiring_team_rating",
                    "comp_median_rating",
                    "hiring_comp_rating",
                    "avg_mins_in_year",
                    "team_elo_suitability",
                    "comp_elo_suitability",
                    "player_min_suitability",
                    "suitability_score_w_min_coalesce",
                    "suitability_rating",
                    "foot",
                    "contract_expiry"
                ]
            )
        tmp_df["target_pos"] = key
        tmp_df["num_players"] = num_needed

        shortlisted_players.append(tmp_df)

    shortlisted_players = pd.concat(shortlisted_players).reset_index(drop=True)

    if shortlisted_players.shape[0] > 0:

        shortlisted_players["role_idx"] = (
            shortlisted_players.reset_index(drop=True)
            .sort_values(
                by=[
                    "target_pos",
                    "primary_ws_position",
                    "suitability_score_w_min_coalesce",
                ],
                ascending=False,
            )
            .groupby("target_pos")
            .cumcount()
        )

        shortlisted_players = shortlisted_players.reset_index(drop=True)

        results3 = shortlisted_players.sort_values(
            by=["suitability_score_w_min_coalesce"], ascending=False
        ).drop_duplicates(subset=["playerId"])
        results3 = shortlisted_players[
            shortlisted_players["role_idx"] < shortlisted_players["num_players"]
        ]

        grid_indices = results3.apply(
            lambda x: formations[formation]["role_grids"][x["target_pos"]][
                x["role_idx"]
            ],
            1,
        )
        results3["grid_idx"] = grid_indices
    else:
        return {"results": []}

    final_results = []

    for _, row in results3.iterrows():
        player_info = {
            "playerId": row["playerId"],
            "fullName": row.get("fullName"),
            "shortName": row.get("shortName"),
            "imageDataURL": row.get("imageDataURL"),
            "firstName": row.get("firstName"),
            "lastName": row.get("lastName"),
            "birthDate": row.get("birthDate"),
            "team_name": row.get("team_name"),
            "birthArea_name": row.get("birthArea_name"),
            "passportArea_name": row.get("passportArea_name"),
            "eu": row.get("eu"),
            "player_url": row.get("player_url"),
            "current_value": row.get("current_value"),
            "primary_ws_position": row.get("primary_ws_position"),
            "foot": validate_foot(row.get("foot")),  # Validate foot value
            "contract_expiry": row.get("contract_expiry"),
        }

        autofill_entry = {
            "player": player_info,
            "playerId": row["playerId"],
            "suitability_score": row["suitability_rating"],
            "x_cordinate": coordinates[row["grid_idx"]][1],
            "y_cordinate": coordinates[row["grid_idx"]][0],
            "index_position": row["grid_idx"],
            "transfer_fee": row.get("club_asking_price"),
            "asking_salary": row.get("current_gross_salary"),
        }

        final_results.append(autofill_entry)

    return final_results

    # return field_player


@router.get("/{id}", response_model=FootballField)
def read_football_field(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get football_field by ID.
    """
    utils.check_module_access(current_user, "shadow_squads")
    utils.check_get_one(FootballField, current_user)

    football_field = crud.football_field.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    if not football_field:
        raise HTTPException(
            status_code=404,
            detail=("The football_field with this id does not exist in the system"),
        )
    return FootballField.from_orm(football_field)

@router.post("/share-tokens", response_model=str)
def create_token_football_field_endpoint(
    payload: ShareTokenCreate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    utils.check_module_access(current_user, "shadow_squads")
    football_field = crud.football_field.get_by_org(db=db, id=payload.resource_id, org_id=current_user.organization_id)
    if not football_field:
        raise HTTPException(status_code=404, detail="Football field not found.")

    if not bool(payload.expires_at):
        payload.expires_at = datetime.now(timezone.utc) + timedelta(days=365 * 100) 
    token_value = create_share_token(
        db=db,
        resource_type='football_field',
        resource_id=payload.resource_id,
        expires_at=payload.expires_at,
        hide_field_view=payload.hide_field_view,
    )

    url = get_url_frontend()
    
    # Return the full shareable URL or just the token
    share_url = f"{url}/share/shadow-squads/{token_value}"
    return share_url

@router.get("/share/{token}", response_model=FootballFieldShare)
def get_shared_resource(token: str, db: Session = Depends(deps.get_db)):
    # 1. Lookup token in DB
    share_token = db.query(models.ShareToken).filter(models.ShareToken.token == token).first()
    if not share_token:
        raise HTTPException(status_code=404, detail="Share token not found.")
    
    if share_token.is_disabled:
        raise HTTPException(status_code=403, detail="Share token disabled.")
    
    if share_token.expires_at < datetime.now():
        raise HTTPException(status_code=403, detail="Share token expired.")
    
    # 5. Fetch the actual resource
    football_field = crud.football_field.get(db=db, id=share_token.resource_id)
    organization_logo = crud.organization.get(db=db, id=football_field.organization_id).agency_logo_url
    football_field.org_logo = organization_logo
    if not football_field:
        raise HTTPException(status_code=404, detail="Resource not found.")
    
    football_field.hide_field_view = getattr(share_token, 'hide_field_view', False)
    
    return football_field


@router.put("/{id}", response_model=FootballField)
@audit_action(
    action_type="update",
    record_type="football_field",
    get_original_object=lambda kwargs: crud.football_field.get_by_org(
        kwargs['db'], kwargs['id'], kwargs['current_user'].organization_id
    )
)
async def update_football_field(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    request: Request,
    obj_in: FootballFieldUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update an football_field.
    """
    utils.check_module_access(current_user, "shadow_squads")
    utils.check_modify(FootballField, current_user)

    football_field = crud.football_field.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    if not football_field:
        raise HTTPException(
            status_code=404,
            detail=("The football_field with this id does not exist in the system"),
        )

    football_field = crud.football_field.update(
        db=db,
        db_obj=football_field,
        obj_in=obj_in,
    )

    return football_field


@router.put("/player/{id}", response_model=FieldPlayers)
async def update_field_player(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    obj_in: FieldPlayersUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a player on a field.
    """
    field_player = crud.field_player.get(db=db, id=id)

    if not field_player:
        raise HTTPException(
            status_code=404,
            detail="The field_player with this id does not exist in the system",
        )

    field_player = crud.field_player.update(
        db=db,
        db_obj=field_player,
        obj_in=obj_in,
    )

    return field_player


@router.delete("/{id}", response_model=FootballField)
@audit_action(action_type="delete", record_type="football_field",)
def delete_football_field(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    request: Request,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete an football_field.
    """
    utils.check_module_access(current_user, "shadow_squads")
    utils.check_delete(FootballField, current_user)

    football_field = crud.football_field.get_by_org(db=db, id=id, org_id=current_user.organization_id)
    if not football_field:
        raise HTTPException(
            status_code=404,
            detail=("The football_field with this id does not exist in the system"),
        )

    football_field = crud.football_field.remove(db=db, id=id)
    return football_field


@router.delete("/remove_player/{id}")
def delete_field_player(
    *,
    db: Session = Depends(deps.get_db),
    id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Remove a player from a field.
    """

    field_player = crud.field_player.get(db=db, id=id)

    if not field_player:
        raise HTTPException(
            status_code=404,
            detail="The field_player with this id does not exist in the system",
        )
    field_player = crud.field_player.remove(db=db, id=id)
    return None


@router.delete("/player/bulk-delete")
async def delete_all_players(
    *,
    db: Session = Depends(deps.get_db),
    field_id: Optional[str] = None,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Removes all players from the football field.
    """
    utils.check_module_access(current_user, "shadow_squads")
    deleted = crud.field_player.delete_all_players(db=db, field_id=field_id)

    return deleted


@router.get("/pdf/{field_id}", response_model=FootballField)
async def download_shadow_pdf(
    *,
    db: Session = Depends(deps.get_db),
    field_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get football_field by ID.
    """
    utils.check_module_access(current_user, "shadow_squads")
    football_field = crud.football_field.get_by_org(db=db, id=field_id, org_id=current_user.organization_id)
    #organization_logo_url = crud.organization.get_logo_by_id(db=db, organization_id=current_user.organization_id)
    players_data = []
    try:
        field_data = {
            "name": football_field.name, 
            "teamImg": football_field.team.imageDataURL, 
            "club": football_field.club_name, 
            "has_hardcoded": False,
            "contact_name": football_field.contact_name,
            "contact_email": football_field.contact_email,
            "contact_phone": football_field.contact_phone
        }
    except:
        field_data = {
            "name": football_field.name, 
            "teamImg": None, 
            "club": football_field.club_name, 
            "has_hardcoded": False,
            "contact_name": football_field.contact_name,
            "contact_email": football_field.contact_email,
            "contact_phone": football_field.contact_phone
        }
    #if organization_logo_url:
    #    field_data['has_hardcoded'] = organization_logo_url
    index_pos_set = set()
    for player in football_field.players:
        player_info = player.player
        age = None
        try:
            age = (
                datetime.now() - datetime.strptime(player_info.birthDate, "%Y-%m-%d")
            ).days // 365
        except:
            pass
        
        if player.transfer_fee == -1:
            player.transfer_fee = "Loan"
        elif player.transfer_fee:
            player.transfer_fee = format_number(player.transfer_fee)
        elif player.transfer_fee == 0:
            player.transfer_fee = "Free"
        else:
            player.transfer_fee = "N/A"
        if not player_info.passportArea_name:
            player_info.passportArea_name = 'None'
        if not player_info.birthArea_name:
            player_info.birthArea_name = 'None'
        if player_info.primary_ws_position in positionMappingWS:
            pos = positionMappingWS[player_info.primary_ws_position].upper()
        else:
            pos = None
        dd = {
            "name": (
                player_info.shortName
                if player_info.shortName
                else f"{player_info.firstName} {player_info.lastName}"
            ),
            "team": player_info.team_name,
            "playerImg": player_info.imageDataURL,
            "transfer_fee": player.transfer_fee,
            "salary": (
                format_number(player.asking_salary) if player.asking_salary else "N/A"
            ),
            "age": age,
            "link": player.video_link if player.video_link else player_info.player_url,
            "passport": f"https://flagcdn.com/w40/{contries_dict[player_info.passportArea_name]}.png",
            "nationality": f"https://flagcdn.com/w40/{contries_dict[player_info.birthArea_name]}.png",
            "x_px": f"{int(player.x_cordinate * 11.25)}px",  # because 100% = 850px wide pitch
            "y_px": f"{int(player.y_cordinate * 11.7)}px",
            "position": pos
        }
        index_pos_set.add(player.index_position)
        players_data.append(dd)
    pdf_content = generate_pdf(players_data, field_data)

    filename = f"{football_field.name}_{football_field.club_name}.pdf"
    quoted_filename = quote(filename)

    headers = {
        # Safe for non-ASCII names
        "Content-Disposition": f"attachment; filename*=UTF-8''{quoted_filename}"
    }

    return StreamingResponse(BytesIO(pdf_content), media_type="application/pdf", headers=headers)


@router.get("/download/{token}", response_model=FootballField)
def get_shared_resource_pdf(token: str, db: Session = Depends(deps.get_db)):
    # 1. Lookup token in DB
    share_token = db.query(models.ShareToken).filter(models.ShareToken.token == token).first()
    if not share_token:
        raise HTTPException(status_code=404, detail="Share token not found.")
    
    if share_token.is_disabled:
        raise HTTPException(status_code=403, detail="Share token disabled.")
    
    if share_token.expires_at < datetime.now():
        raise HTTPException(status_code=403, detail="Share token expired.")
    
    # 5. Fetch the actual resource
    football_field = crud.football_field.get(db=db, id=share_token.resource_id)
    
    #organization_logo_url = crud.organization.get_logo_by_id(db=db, organization_id=current_user.organization_id)
    players_data = []
    try:
        field_data = {
            "name": football_field.name, 
            "teamImg": football_field.team.imageDataURL, 
            "club": football_field.club_name, 
            "has_hardcoded": False,
            "contact_name": football_field.contact_name,
            "contact_email": football_field.contact_email,
            "contact_phone": football_field.contact_phone
        }
    except:
        field_data = {
            "name": football_field.name, 
            "teamImg": None, 
            "club": football_field.club_name, 
            "has_hardcoded": False,
            "contact_name": football_field.contact_name,
            "contact_email": football_field.contact_email,
            "contact_phone": football_field.contact_phone
        }
    
    #if organization_logo_url:
    #    field_data['has_hardcoded'] = organization_logo_url
    index_pos_set = set()
    for player in football_field.players:
        player_info = player.player
        age = None
        try:
            age = (
                datetime.now() - datetime.strptime(player_info.birthDate, "%Y-%m-%d")
            ).days // 365
        except:
            pass
        
        if player.transfer_fee == -1:
            player.transfer_fee = "Loan"
        elif player.transfer_fee:
            player.transfer_fee = format_number(player.transfer_fee)
        else:
            player.transfer_fee = "Free"
        if not player_info.passportArea_name:
            player_info.passportArea_name = 'None'
        if not player_info.birthArea_name:
            player_info.birthArea_name = 'None'

        dd = {
            "name": (
                player_info.shortName
                if player_info.shortName
                else f"{player_info.firstName} {player_info.lastName}"
            ),
            "team": player_info.team_name,
            "playerImg": player_info.imageDataURL,
            "transfer_fee": player.transfer_fee,
            "salary": (
                format_number(player.asking_salary) if player.asking_salary else "N/A"
            ),
            "age": age,
            "link": player.video_link if player.video_link else player_info.player_url,
            "passport": f"https://flagcdn.com/w40/{contries_dict[player_info.passportArea_name]}.png",
            "nationality": f"https://flagcdn.com/w40/{contries_dict[player_info.birthArea_name]}.png",
            "x_px": f"{int(player.x_cordinate * 11.25)}px",  # because 100% = 850px wide pitch
            "y_px": f"{int(player.y_cordinate * 11.7)}px",
            "position": player_info.primary_ws_position
        }
        index_pos_set.add(player.index_position)
        players_data.append(dd)
    pdf_content = generate_pdf(players_data, field_data)

    filename = f"{football_field.name}_{football_field.club_name}.pdf"
    quoted_filename = quote(filename)

    headers = {
        # Safe for non-ASCII names
        "Content-Disposition": f"attachment; filename*=UTF-8''{quoted_filename}"
    }

    return StreamingResponse(BytesIO(pdf_content), media_type="application/pdf", headers=headers)
