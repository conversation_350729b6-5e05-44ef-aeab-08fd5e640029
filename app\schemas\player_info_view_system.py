from typing import Optional
from pydantic import BaseModel

from app.schemas.enums import Foot
from datetime import date

class PlayerInfoSystemShort(BaseModel):
    playerId: int
    firstName: str
    lastName: str
    shortName: Optional[str] = None
    birthDate: Optional[str]
    team_name: Optional[str] = None
    currentTeamId: Optional[int] = None
    passport: Optional[str] = None
    tm_link: Optional[str] = None

    class Config:
        orm_mode = True
        use_cache=True
class PlayerInfoSystem(BaseModel):
    playerId: int
    firstName: str
    lastName: str
    shortName: Optional[str] = None
    birthArea_name: Optional[str] = None
    birthDate: Optional[str]
    team_name: Optional[str] = None
    divisionLevel: Optional[int] = None
    team_area_name: Optional[str] = None
    currentTeamId: Optional[int] = None
    imageDataURL: Optional[str] = None
    passportArea_name: Optional[str] = None
    height: Optional[str] = None
    eu: Optional[bool]
    foot: Optional[Foot]
    player_url: Optional[str] = None
    current_value: Optional[float] = None
    agent: Optional[str]
    primary_ws_position: Optional[str]
    secondary_ws_position: Optional[str]
    third_ws_position: Optional[str]
    contract_expiry: Optional[date]
    date_joined_current_team: Optional[date]
    
    class Config:
        orm_mode = True
        use_cache=True