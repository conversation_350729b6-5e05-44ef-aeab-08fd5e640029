from typing import List, Optional
from sqlalchemy.orm import Session, selectinload
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException

from app.crud.crud_base import CRUDBase
from app import models
from app.schemas.favourite_teams import (
    FavouriteTeamCreate,
    FavouriteTeamRead,
    FavouriteTeamUpdate,
)


class CRUDFavouriteTeams(
    CRUDBase[models.FavouriteTeams, FavouriteTeamCreate, FavouriteTeamUpdate]
):

    def create_with_user(
        self, db: Session, *, obj_in: FavouriteTeamCreate, user_id: str
    ) -> models.FavouriteTeams:
        """Create a favourite team for a specific user"""
        try:
            db_obj = self.model(user_id=user_id, teamId=obj_in.teamId)
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
            return db_obj
        except IntegrityError:
            db.rollback()
            raise HTTPException(
                status_code=409,
                detail="Team is already marked as favourite for this user",
            )

    def get_by_user(self, db: Session, user_id: str) -> List[models.FavouriteTeams]:
        """Get all favourite teams for a specific user"""
        return (
            db.query(self.model)
            .options(selectinload(self.model.user), selectinload(self.model.team_info))
            .filter(self.model.user_id == user_id)
            .all()
        )

    def delete_by_user_and_team(self, db: Session, user_id: str, teamId: int) -> bool:
        """Delete a favourite team for a specific user"""
        db_obj = (
            db.query(self.model)
            .filter(self.model.user_id == user_id, self.model.teamId == teamId)
            .first()
        )
        if db_obj:
            db.delete(db_obj)
            db.commit()
            return True
        return False

    def is_favourite_for_user(self, db: Session, user_id: str, teamId: int) -> bool:
        """Check if a team is marked as favourite for a specific user"""
        return (
            db.query(self.model)
            .filter(self.model.user_id == user_id, self.model.teamId == teamId)
            .first()
        ) is not None


favourite_teams = CRUDFavouriteTeams(models.FavouriteTeams)
