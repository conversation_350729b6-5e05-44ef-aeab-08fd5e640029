"""Favourite team - only one team per user

Revision ID: 48c2a9a47e31
Revises: 98a7a884fca6
Create Date: 2025-08-27 10:47:32.534507

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '48c2a9a47e31'
down_revision = '98a7a884fca6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('favourite_teams', 'user_id',
               existing_type=sa.UUID(),
               nullable=False,
               schema='crm_test')
    op.alter_column('favourite_teams', 'teamId',
               existing_type=sa.INTEGER(),
               nullable=False,
               schema='crm_test')
    op.drop_index('ix_crm_test_favourite_teams_id', table_name='favourite_teams', schema='crm_test')
    op.drop_index('ix_crm_test_favourite_teams_teamId', table_name='favourite_teams', schema='crm_test')
    op.drop_index('ix_crm_test_favourite_teams_user_id', table_name='favourite_teams', schema='crm_test')
    op.drop_column('favourite_teams', 'id', schema='crm_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('favourite_teams', sa.Column('id', sa.UUID(), autoincrement=False, nullable=False), schema='crm_test')
    op.create_index('ix_crm_test_favourite_teams_user_id', 'favourite_teams', ['user_id'], unique=False, schema='crm_test')
    op.create_index('ix_crm_test_favourite_teams_teamId', 'favourite_teams', ['teamId'], unique=False, schema='crm_test')
    op.create_index('ix_crm_test_favourite_teams_id', 'favourite_teams', ['id'], unique=False, schema='crm_test')
    op.alter_column('favourite_teams', 'teamId',
               existing_type=sa.INTEGER(),
               nullable=True,
               schema='crm_test')
    op.alter_column('favourite_teams', 'user_id',
               existing_type=sa.UUID(),
               nullable=True,
               schema='crm_test')
    # ### end Alembic commands ###