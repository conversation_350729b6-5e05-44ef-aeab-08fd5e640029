"""Add tags to player_records

Revision ID: 80cdb12f33ea
Revises: ee52f1c3edbb
Create Date: 2025-08-13 16:00:59.698776

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '80cdb12f33ea'
down_revision = 'ee52f1c3edbb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('player_records', sa.Column('tags', postgresql.ARRAY(sa.String()), nullable=True), schema='crm_test')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('player_records', 'tags', schema='crm_test')
    # ### end Alembic commands ###