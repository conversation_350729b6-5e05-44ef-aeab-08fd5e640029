import uuid
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Float
from sqlalchemy.orm import relationship, backref
from datetime import datetime
from app.config import settings
from app.db.base_class import Base
from app.models.extended_base_mixin import ExtendedBaseMixin


class FootballField(Base, ExtendedBaseMixin):
    __tablename__ = "football_fields"
    __table_args__ = {"schema": settings.PG_SCHEMA}

    name = Column(String, index=True)
    organization_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.organizations.id"),
        index=True,
    )
    players = relationship(
        "FieldPlayers",
        primaryjoin="FootballField.id==FieldPlayers.field_id",
        back_populates="football_field",
        # cascade="all, delete-orphan", lazy='subquery', backref=backref('field_players', cascade='delete'),
    )
    club_name = Column(String)
    teamId = Column(<PERSON><PERSON><PERSON>, ForeignKey("wyscout.team_info2.teamId"), index=True)
    team = relationship(
        "TeamInfo",
        primaryjoin="foreign(FootballField.teamId)==remote(TeamInfo.teamId)",
        viewonly=True,
    )
    for_date = Column(DateTime, default=datetime.now)
    formation = Column(String)
    uploads = relationship("LogoUpload", back_populates="football_field")
    contact_name = Column(String)
    contact_email = Column(String)
    contact_phone = Column(String)



class LogoUpload(Base, ExtendedBaseMixin):
    __tablename__ = "logo_upload"
    __table_args__ = {"schema": settings.PG_SCHEMA}
    id = Column(
        String,
        primary_key=True,
    )
    football_field_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.football_fields.id"),
        index=True,
    )
    football_field = relationship("FootballField", back_populates="uploads")


class FieldPlayers(Base):
    __tablename__ = "field_players"
    __table_args__ = {"schema": settings.PG_SCHEMA}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    field_id = Column(
        UUID(as_uuid=True),
        ForeignKey(f"{settings.PG_SCHEMA}.football_fields.id"),
        index=True,
    )
    playerId = Column(
        Integer,
        ForeignKey(f"wyscout.player_info_view_system.playerId"),
        index=True,
    )

    player = relationship(
        "PlayerInfoViewSystem",
        viewonly=True,
        primaryjoin="FieldPlayers.playerId==PlayerInfoViewSystem.playerId",
    )
    index_position = Column(Integer)
    x_cordinate = Column(Float)
    y_cordinate = Column(Float)
    suitability_score = Column(Float)
    football_field = relationship(
        "FootballField", back_populates="players", lazy="joined"
    )
    transfer_fee = Column(Float)
    asking_salary = Column(Float)
    video_link = Column(String)
    contract_expiry = Column(DateTime)
    foot = Column(String)
    description = Column(String)