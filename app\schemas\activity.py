from app.schemas.extended_base import (
    ExtendedBase,
    ExtendedUpdateBase,
    ExtendedCreateBase,
)
from typing import Optional, List
import uuid
from app.schemas.enums import ActivityStage, ActivityType
from pydantic import BaseModel
from datetime import datetime, date
from app.schemas.player_info_view_crm import PlayerInfoViewCrmShort
from app.schemas.team_info import TeamInfo
from app.schemas.contact import ContactShort

from app.schemas.comment import Comment
from app.schemas.assigned_to_record import AssignedToRecord
from app.schemas.staff_info import StaffInfo


# WhatsApp Activity Lookup Schemas
class ActivityMatch(BaseModel):
    id: str
    title: str
    type: str
    created_at: datetime
    similarity_score: float

    class Config:
        schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "title": "Negotiate with Real Madrid",
                "type": "deal",
                "created_at": "2024-01-15T10:30:00Z",
                "similarity_score": 0.85,
            }
        }


class WhatsAppActivityCheckResponse(BaseModel):
    id: str
    title: str
    type: str
    created_at: datetime
    similarity_score: float
    top_matches: List[ActivityMatch]

    class Config:
        schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "title": "Negotiate with Barcelona",
                "type": "deal",
                "created_at": "2024-01-15T10:30:00Z",
                "similarity_score": 0.95,
                "top_matches": [
                    {
                        "id": "123e4567-e89b-12d3-a456-426614174100",
                        "title": "Negotiate with Real Madrid",
                        "type": "deal",
                        "created_at": "2024-01-10T09:15:00Z",
                        "similarity_score": 0.75,
                    },
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "title": "Meeting with Valencia",
                        "type": "task",
                        "created_at": "2024-01-08T14:20:00Z",
                        "similarity_score": 0.65,
                    },
                ],
            }
        }


class WhatsAppActivityUpdate(ExtendedUpdateBase):
    stage: Optional[ActivityStage]
    due_date: Optional[date]
    next_action: Optional[date]
    assigned_to_record: Optional[List[uuid.UUID]]
    updated_by_id: Optional[uuid.UUID]

    class Config:
        schema_extra = {
            "example": {
                "stage": "in_progress",
                "due_date": "2024-02-15",
                "next_action": "2024-02-10",
                "assigned_to_record": ["123e4567-e89b-12d3-a456-************"],
            }
        }


class ActivityUpdate(ExtendedUpdateBase):
    assigned_to_record: Optional[List[uuid.UUID]]
    updated_by_id: Optional[uuid.UUID]
    teamId: Optional[int]
    staff: Optional[str]
    stage: Optional[ActivityStage]
    next_action: Optional[date]
    due_date: Optional[date]
    title: Optional[str]
    description: Optional[str]
    date_deal_created: Optional[date]


class ActivityCreate(ActivityUpdate, ExtendedCreateBase):
    type: ActivityType
    title: str
    playerId: Optional[int]
    staff_id: Optional[int]


class QuickCreateActivityPlayer(BaseModel):
    teamId: int
    team_name: str
    playerId: int
    player_name: str
    stage: Optional[ActivityStage] = "offered"
    type: ActivityType = "deal"


class QuickCreateOpActivity(BaseModel):
    teamId: int
    team_name: str
    playerId: int
    player_name: str
    description: Optional[str]


class QuickCreateStaffActivity(BaseModel):
    teamId: int
    team_name: str
    staff_id: int
    staff_name: str
    stage: Optional[ActivityStage] = "offered"
    type: ActivityType = "deal"


class Activity(ActivityCreate, ExtendedBase):
    player_info_view_crm: Optional[PlayerInfoViewCrmShort]
    team_info: Optional[TeamInfo]
    staff_info: Optional[StaffInfo]
    # changelog: List[Change]
    # updated_by: Optional[ContactShort]
    assigned_to_record: Optional[List[AssignedToRecord]]
    comments: List[Comment]


class ActivityDeal(ExtendedBase):
    type: ActivityType
    team_info: TeamInfo
    staff: Optional[str]
    # changelog: List[Change]  # Replaced with new audit log system
    stage: ActivityStage
    next_action: Optional[date]
    updated_by: Optional[ContactShort]
    assigned_to_record: Optional[List[AssignedToRecord]]


class ActivityTask(ExtendedBase):
    type: ActivityType
    team_info: Optional[TeamInfo]
    due_date: Optional[date]
    stage: ActivityStage
    # changelog: List[Change]  # Replaced with new audit log system
    updated_by: Optional[ContactShort]
    assigned_to_record: Optional[List[AssignedToRecord]]

    class Config:
        use_cache = True
        orm_mode = True
