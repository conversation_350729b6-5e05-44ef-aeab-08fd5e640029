#!/usr/bin/env python3
"""
Performance test script for team requests endpoint.
This script will help identify bottlenecks in the team requests API.
"""

import requests
import time
import json
import sys
from typing import Optional

def test_team_requests_performance(
    base_url: str = "http://localhost:8000",
    auth_token: Optional[str] = None,
    active_filter: Optional[bool] = None
):
    """
    Test the performance of the team requests endpoint.
    
    Args:
        base_url: Base URL of the API
        auth_token: JWT token for authentication
        active_filter: Optional filter for active requests
    """
    
    # Prepare headers
    headers = {
        "Content-Type": "application/json"
    }
    
    if auth_token:
        headers["Authorization"] = f"Bearer {auth_token}"
    
    # Prepare URL with query parameters
    url = f"{base_url}/api/v1/team-requests/"
    params = {}
    if active_filter is not None:
        params["active"] = active_filter
    
    print(f"Testing team requests endpoint: {url}")
    print(f"Parameters: {params}")
    print(f"Headers: {headers}")
    print("-" * 50)
    
    # Measure total request time
    start_time = time.time()
    
    try:
        response = requests.get(url, headers=headers, params=params, timeout=30)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"Response Status: {response.status_code}")
        print(f"Total Request Time: {total_time:.3f}s")
        
        if response.status_code == 200:
            data = response.json()
            
            # Analyze response structure
            private_count = len(data.get("private", []))
            community_count = len(data.get("community", []))
            
            print(f"Private Requests: {private_count}")
            print(f"Community Requests: {community_count}")
            print(f"Total Records: {private_count + community_count}")
            
            # Calculate average time per record
            total_records = private_count + community_count
            if total_records > 0:
                avg_time_per_record = total_time / total_records
                print(f"Average Time per Record: {avg_time_per_record:.4f}s")
            
            # Sample response structure (first record only)
            if private_count > 0:
                print("\nSample Private Request Structure:")
                sample_private = data["private"][0]
                print(f"  Fields: {list(sample_private.keys())}")
                
            if community_count > 0:
                print("\nSample Community Request Structure:")
                sample_community = data["community"][0]
                print(f"  Fields: {list(sample_community.keys())}")
                
        else:
            print(f"Error Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("Request timed out after 30 seconds")
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

def main():
    """
    Main function to run the performance test.
    """
    
    # You can modify these parameters as needed
    base_url = "http://localhost:8000"  # Change to your API URL
    auth_token = None  # Add your JWT token here if needed
    
    print("Team Requests Performance Test")
    print("=" * 50)
    
    # Test with different filters
    test_cases = [
        {"name": "All Requests", "active_filter": None},
        {"name": "Active Requests Only", "active_filter": True},
        {"name": "Inactive Requests Only", "active_filter": False},
    ]
    
    for test_case in test_cases:
        print(f"\n{test_case['name']}")
        print("-" * 30)
        test_team_requests_performance(
            base_url=base_url,
            auth_token=auth_token,
            active_filter=test_case["active_filter"]
        )
        
        # Wait between tests
        time.sleep(1)
    
    print("\n" + "=" * 50)
    print("Performance test completed!")
    print("\nTo see detailed performance logs, check your application logs for entries with:")
    print("  [TEAM_REQUESTS_PERF] - Endpoint-level performance metrics")
    print("  [CRUD_PERF] - Database query performance metrics")

if __name__ == "__main__":
    main()
