from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
import logging
import time

from app.config import settings

# Set up SQL query logging
sql_logger = logging.getLogger('sqlalchemy.engine')
sql_logger.setLevel(logging.INFO)

# Create engines with performance monitoring
async_engine = create_async_engine(settings.PG_URL_ASYNC, pool_use_lifo=True, pool_pre_ping=True)
engine = create_engine(settings.PG_URL,
    use_insertmanyvalues='insertmanyvalues',
    insertmanyvalues_page_size=1000,
    pool_use_lifo=True, pool_pre_ping=True)

# Add SQL query performance logging
@event.listens_for(engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    context._query_start_time = time.time()

@event.listens_for(engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    total = time.time() - context._query_start_time
    if total > 0.1:  # Log queries that take longer than 100ms
        # Truncate long queries for readability
        query_preview = statement.replace('\n', ' ').replace('\t', ' ')
        if len(query_preview) > 200:
            query_preview = query_preview[:200] + "..."
        sql_logger.info(f"[SQL_PERF] Slow query ({total:.3f}s): {query_preview}")

async_session_maker = sessionmaker(async_engine, class_=AsyncSession, expire_on_commit=False)
session_maker = sessionmaker(autocommit=False, autoflush=False, bind=engine)