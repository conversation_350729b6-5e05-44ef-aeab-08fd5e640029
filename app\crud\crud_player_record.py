import uuid
from fastapi.encoders import jsonable_encoder
from typing import Any, Dict, List, Optional, Type, TypeVar, Union
from pydantic import BaseModel
from sqlalchemy.orm import Session, lazyload, Load, selectinload
from app.crud.crud_base import CRUDBase
from app import models
from app.db.base_class import Base
from app.models import User
from app.utils import compare_version_of_objects
import json
from app.utils import control_stages
from sqlalchemy import text
from datetime import datetime
from app.config import settings

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)
ChangeType = TypeVar("ChangeType", bound=Base)


class CRUDPlayerRecord(CRUDBase):
    def get_all_with_filters(
        self,
        db: Session,
        org_id: str,
        can_access_sensitive: bool,
        filters: dict,
        user_id: str = None,
    ) -> List[ModelType]:
        # Base query with LEFT JOIN to get priority status
        from sqlalchemy import case, and_

        if user_id:
            query = (
                db.query(
                    self.model,
                    case(
                        (models.PriorityPlayers.id.isnot(None), True), else_=False
                    ).label("is_priority"),
                )
                .outerjoin(
                    models.PriorityPlayers,
                    and_(
                        models.PriorityPlayers.player_record_id == self.model.id,
                        models.PriorityPlayers.user_id == user_id,
                    ),
                )
                .options(
                    selectinload(self.model.player_info),
                    selectinload(self.model.player_info_view_crm),
                    selectinload(self.model.organization),
                    selectinload(self.model.creator),
                    selectinload(self.model.assigned_to_record).selectinload(
                        models.AssignedToRecord.contact
                    ),
                )
                .filter(self.model.organization_id == org_id)
            )
        else:
            # Fallback for when no user_id is provided
            query = (
                db.query(self.model)
                .options(
                    selectinload(self.model.player_info_view_crm),
                    selectinload(self.model.organization),
                    selectinload(self.model.creator),
                    selectinload(self.model.assigned_to_record).selectinload(
                        models.AssignedToRecord.contact
                    ),
                )
                .filter(self.model.organization_id == org_id)
            )

        # Apply sensitive data filter
        if not can_access_sensitive:
            query = query.filter(self.model.is_sensitive != True)

            # Apply control_stage filter (if provided)
        if filters.get("control_stage"):
            query = query.filter(self.model.control_stage.in_(filters["control_stage"]))

        if "active" in filters and filters["active"] is not None:
            if filters["active"]:
                query = query.filter(self.model.control_stage.in_(control_stages))
            else:
                query = query.filter(self.model.control_stage == "closed")

        # Apply priority filter (if provided and user_id exists)
        if user_id and "priority" in filters and filters["priority"] is not None:
            if filters["priority"]:
                # Only show priority players for this user
                query = query.filter(models.PriorityPlayers.id.isnot(None))
            else:
                # Only show non-priority players for this user
                query = query.filter(models.PriorityPlayers.id.is_(None))

        # Order by last_updated
        query = query.order_by(self.model.last_updated.desc())

        return query.all()

    def create_with_user(
        self, db: Session, *, obj_in: CreateSchemaType, user: User
    ) -> ModelType:
        obj_in_data = jsonable_encoder(obj_in)
        assigned_in_data = obj_in_data.pop("assigned_to_record")

        ll_assignees = [
            models.AssignedToRecord(contact_id=con, id=uuid.uuid4())
            for con in assigned_in_data
        ]
        db_obj = self.model(
            **obj_in_data,
            created_by=user.id,
            organization_id=user.organization_id,
            assigned_to_record=ll_assignees,
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    def get_with_wyscout(self, db: Session, wyId: Any, org_id) -> Optional[ModelType]:
        return (
            db.query(self.model)
            .options(
                Load(self.model).selectinload("*"),
                lazyload("*"),
            )
            .filter(self.model.playerId == wyId, self.model.organization_id == org_id)
            .first()
        )

    def get_minutes_played(self, db: Session, wyId: Any) -> Optional[int]:
        query = text(
            f'select * from wyscout.player_year_minutes psm where "playerId" = :wyId'
        )
        result = db.execute(query, {"wyId": wyId})
        return result.mappings().first()

    def get_stats_for_playerId(self, db: Session, playerId: int):
        query = text(
            """
            SELECT
                SUM(goals) AS goals,
                COUNT("matchId") AS appearances,
                pym.total_mins_in_year,
                as2."playerId"
            FROM wyscout.advanced_stats as2
            JOIN wyscout.player_year_minutes pym ON pym."playerId" = as2."playerId"
            WHERE as2."playerId" = :playerId
            GROUP BY as2."playerId", pym.total_mins_in_year
        """
        )
        resultproxy = db.execute(query, {"playerId": playerId})
        return resultproxy.mappings().first()

    def get_transfers_for_playerId(
        self, db: Session, playerId: int
    ) -> Optional[List[ModelType]]:
        query = """
        SELECT "fromTeamName", "toTeamName", "startDate" AS date, type
        FROM wyscout.transfers t
        WHERE "playerId" = :playerId
        ORDER BY date DESC
        """
        result = db.execute(text(query), {"playerId": playerId})
        return result.mappings().all()

    def get_wyscout_player_data_playerId(self, db: Session, playerId: int):
        query = text(
            f"""
            SELECT *
            FROM wyscout.{settings.PLAYER_INFO_VIEW_SYSTEM}
            WHERE "playerId" = :playerId
        """
        )
        return db.execute(query, {"playerId": playerId}).mappings().first()

    def get_wyscout_player_data_tmId(self, db: Session, tm_player_id: int):
        query = text(
            f"""
            SELECT pi2.*
            FROM wyscout.{settings.PLAYER_INFO_VIEW_SYSTEM} pi2
            JOIN transfermarkt.tm_to_ws_ids ttwi ON ttwi."playerId" = pi2."playerId"
            WHERE ttwi.tm_player_id = :tm_player_id
        """
        )
        return db.execute(query, {"tm_player_id": tm_player_id}).mappings().first()

    # def get_all_w_suitability(self, db: Session, org_id, can_access_sensitive, teamId) -> Optional[int]:
    #     # return self.get_all_w_org(db, org_id, can_access_sensitive)
    #     filter_cond = self.model.is_sensitive != True if not can_access_sensitive else True
    #     return db.query(self.model, SuitScores).join(SuitScores, self.model.playerId==SuitScores.playerId).options(
    #         Load(self.model).selectinload("*"),
    #         lazyload("*"),
    #         ).filter(SuitScores.hiring_team_id == teamId,self.model.organization_id == org_id, filter_cond).all()


player_record = CRUDPlayerRecord(models.PlayerRecord)
