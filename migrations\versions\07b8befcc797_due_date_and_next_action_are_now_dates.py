"""Due date and next action are now dates

Revision ID: 07b8befcc797
Revises: e0066ea1d944
Create Date: 2025-07-09 13:06:46.431348

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '07b8befcc797'
down_revision = 'e0066ea1d944'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('activity', 'next_action',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.Date(),
               existing_nullable=True,
               schema='crm')
    op.alter_column('activity', 'due_date',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.Date(),
               existing_nullable=True,
               schema='crm')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('activity', 'due_date',
               existing_type=sa.Date(),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True,
               schema='crm')
    op.alter_column('activity', 'next_action',
               existing_type=sa.Date(),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True,
               schema='crm')
    # ### end Alembic commands ###