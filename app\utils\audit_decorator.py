import functools
import uuid
from typing import Any, Callable, Dict, Optional, Union
from fastapi import Request
from sqlalchemy.orm import Session
from fastapi.encoders import jsonable_encoder
import asyncio
import inspect
from app import models
from app.utils.audit_logging import log_audit_event, compare_and_log_changes
from app.utils import excluded_fields
from app.schemas.enums import ActionType


def audit_action(
    action_type: ActionType,
    record_type: str,
    get_record_id: Optional[Callable] = None,
    get_record_name: Optional[Callable] = None,
    exclude_fields: Optional[list] = None,
    batch_operation: bool = False,
    get_original_object: Optional[Callable] = None,
):
    """
    Decorator to automatically log audit events for CRUD operations.

    Args:
        action_type: Type of action (create, update, delete)
        record_type: Type of record being modified
        get_record_id: Function to extract record ID from function args/result
        get_record_name: Function to extract record name from function args/result
        exclude_fields: Fields to exclude from change tracking
        batch_operation: Whether this is a batch operation that should use batch_id
        get_original_object: Function to get original object before update (for update actions)
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract common parameters
            db: Optional[Session] = None
            current_user: Optional[models.User] = None
            request: Optional[Request] = None
            original_object = None

            # Look for db, user, and request in kwargs
            for key, value in kwargs.items():
                if key == "db" and isinstance(value, Session):
                    db = value
                elif key == "current_user" and isinstance(value, models.User):
                    current_user = value
                elif key == "request" and isinstance(value, Request):
                    request = value
            # If not found in kwargs, check args (common patterns)
            if not db and len(args) > 0 and isinstance(args[0], Session):
                db = args[0]

            # For updates, capture the original object before modification
            if action_type == "update" and db and get_original_object:
                try:
                    original_object = jsonable_encoder(get_original_object(kwargs))
                except Exception as e:
                    print(f"Failed to capture original object: {e}")

            if inspect.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            if db and current_user:
                try:
                    batch_id = uuid.uuid4() if batch_operation else None
                    actions_to_log = [result]
                    if type(result) == list:
                        actions_to_log = result
                        batch_id = uuid.uuid4()
                    for action in actions_to_log:
                        if action_type == "create":
                            _log_create_action(
                                db,
                                current_user,
                                request,
                                record_type,
                                action,
                                get_record_id,
                                get_record_name,
                                batch_id,
                            )
                        elif action_type == "update":
                            _log_update_action(
                                db,
                                current_user,
                                request,
                                record_type,
                                action,
                                kwargs,
                                get_record_id,
                                exclude_fields,
                                batch_id,
                                original_object,
                            )
                        elif action_type == "delete":
                            _log_delete_action(
                                db,
                                current_user,
                                request,
                                record_type,
                                action,
                                kwargs,
                                get_record_id,
                                get_record_name,
                                batch_id,
                            )
                        elif action_type == "login":
                            _log_login_action(
                                db,
                                current_user,
                                request,
                                record_type,
                                action,
                                kwargs,
                                get_record_id,
                                get_record_name,
                            )
                except Exception as e:
                    # Log the error but don't fail the original operation
                    print(f"Audit logging failed: {e}")

            return result

        return wrapper

    return decorator


def _log_create_action(
    db: Session,
    user: models.User,
    request: Optional[Request],
    record_type: str,
    result: Any,
    get_record_id: Optional[Callable],
    get_record_name: Optional[Callable],
    batch_id: Optional[uuid.UUID],
):
    """Log create action"""
    record_id = _extract_record_id(result, get_record_id)
    record_name = _extract_record_name(result, get_record_name)
    record_data = jsonable_encoder(result) if hasattr(result, "__dict__") else {}
    log_audit_event(
        db=db,
        user=user,
        action_type="create",
        record_type=record_type,
        record_id=record_id,
        updated=record_data,
        record_name=record_name,
        batch_id=batch_id,
        request=request,
    )


def _log_update_action(
    db: Session,
    user: models.User,
    request: Optional[Request],
    record_type: str,
    result: Any,
    kwargs: Dict[str, Any],
    get_record_id: Optional[Callable],
    exclude_fields: Optional[list],
    batch_id: Optional[uuid.UUID],
    original_object: Optional[Any] = None,
):
    """Log update action"""
    record_id = _extract_record_id(result, get_record_id)
    if exclude_fields:
        exclude_fields.extend(excluded_fields)
    else:
        exclude_fields = excluded_fields
    old_obj_data = original_object if original_object is not None else result
    if old_obj_data and result:
        compare_and_log_changes(
            db=db,
            user=user,
            record_type=record_type,
            record_id=record_id,
            old_obj=old_obj_data,
            new_data=(
                jsonable_encoder(result) if hasattr(result, "__dict__") else result
            ),
            exclude_fields=exclude_fields,
            batch_id=batch_id,
            request=request,
        )
    else:
        # Fallback: log the entire updated object
        record_data = jsonable_encoder(result) if hasattr(result, "__dict__") else {}
        log_audit_event(
            db=db,
            user=user,
            action_type="update",
            record_type=record_type,
            record_id=record_id,
            updated=record_data,
            batch_id=batch_id,
            request=request,
        )


def _log_delete_action(
    db: Session,
    user: models.User,
    request: Optional[Request],
    record_type: str,
    result: Any,
    kwargs: Dict[str, Any],
    get_record_id: Optional[Callable],
    get_record_name: Optional[Callable],
    batch_id: Optional[uuid.UUID],
):
    """Log delete action"""
    record_id = kwargs.get("id")

    if result:
        record_data = jsonable_encoder(result)
        record_name = _extract_record_name(result, get_record_name)
        actual_record_id = _extract_record_id(result, get_record_id) or record_id

        log_audit_event(
            db=db,
            user=user,
            action_type="delete",
            record_type=record_type,
            record_id=actual_record_id,
            previous=record_data,
            record_name=record_name,
            batch_id=batch_id,
            request=request,
        )


def _extract_record_id(
    obj: Any, get_record_id: Optional[Callable]
) -> Union[str, uuid.UUID]:
    """Extract record ID from object"""
    if get_record_id:
        return get_record_id(obj)
    elif hasattr(obj, "id"):
        return obj.id
    else:
        return str(uuid.uuid4())  # Fallback


def _extract_record_name(
    obj: Any, get_record_name: Optional[Callable]
) -> Optional[str]:
    """Extract record name from object"""
    if get_record_name:
        return get_record_name(obj)
    elif hasattr(obj, "name"):
        return obj.name
    elif hasattr(obj, "player_info_view_crm"):
        return (
            obj.player_info_view_crm.shortName
            or obj.player_info_view_crm.firstName
            + " "
            + obj.player_info_view_crm.lastName
        )
    elif hasattr(obj, "title"):
        return obj.title
    elif hasattr(obj, "email"):
        return obj.email
    else:
        return None


def _log_login_action(
    db: Session,
    current_user: models.User,
    request: Optional[Request],
    record_type: str,
    action: Any,
    kwargs: Dict[str, Any],
    get_record_id: Optional[Callable],
    get_record_name: Optional[Callable],
):
    """Log successful login action"""
    record_id = _extract_record_id(current_user, get_record_id)
    record_name = _extract_record_name(current_user, get_record_name)

    # Extract login details from kwargs
    credentials = kwargs.get("credentials")
    remember_me = kwargs.get("remember_me", False)

    login_data = {
        "action": "login",
        "email": credentials.username if credentials else None,
        "remember_me": remember_me,
        "success": True,
    }

    log_audit_event(
        db=db,
        user=current_user,
        action_type="login",
        record_type=record_type,
        record_id=record_id,
        updated=login_data,
        record_name=record_name,
        request=request,
    )


def log_failed_login_attempt(
    db: Session,
    email: str,
    request: Optional[Request] = None,
    error_detail: str = "LOGIN_BAD_CREDENTIALS",
):
    """Log failed login attempt"""
    from app.utils.audit_logging import log_audit_event
    import uuid

    login_data = {
        "action": "login",
        "email": email,
        "success": False,
        "error": error_detail,
    }

    # Create a system user ID for failed logins (you can use a fixed UUID)
    system_user_id = uuid.UUID("00000000-0000-0000-0000-000000000000")

    # For failed logins, we don't have a user object, so create a minimal audit entry
    log_audit_event(
        db=db,
        user=None,  # No user for failed login
        action_type="login",
        record_type="user",
        record_id=uuid.uuid4(),  # random uuid
        updated=login_data,
        record_name=email,
        request=request,
        details=email,
    )
