with main_tbl as (select *
from (
        select sd.staff_id,
            sd.current_team_id,
            tt.joined_name,
            td."shortName",
            tt."date",
            tt.tm_player_id,
            tt.left_name,
            (current_date - sd.birth_date::date) / 365::int as age,
            sd.appointed,
            sd.in_charge_until,
            sd.role,
            sd.name
        from transfermarkt.staff_data sd
            join transfermarkt.tm_transfers tt on tt.joined_id = sd.current_team_id
            join transfermarkt.tm_to_ws_ids ttwi on tt.tm_player_id = ttwi.tm_player_id
            join wyscout.player_info_view_system td on td."playerId" = ttwi."playerId"
            join transfermarkt.tm_teams tt2 on tt2.team_id = tt.left_id
            join transfermarkt.tm_teams tt3 on tt3.team_id = tt.joined_id
        where role in (
                'Manager',
                'Assistant Manager',
                'Caretaker Manager',
                'Sporting Director',
                'Scout',
                'Director of Football',
                'Technical Director',
                'Chief Scout',
                'Chief Analyst',
                'Head of Scouting',
                'Head of Football Operations',
                'Managing Director Sport',
                'Managing Director Professional Football',
                'Director of Professional Football and Scout',
                'Global Sports Director',
                'Sporting CEO, Director of Sport',
                'President',
                'Owner',
                'Chief Executive Officer'
            )
            and sd.status = 'alive'
            and tt.joined_name is not null
            and appointed::date < tt."date"
            and coalesce(in_charge_until, '2023-02-06')::date >= tt."date"
            and tt.new_type != 'back from loan'
            and tt2.league_country in (
                select country_name
                from public.countries
                where region = 'bulgaria'
            )
            and tt3.league_country not in (
                select country_name
                from public.countries
                where region = 'bulgaria'
            )
            and td."birthArea_name" in (
                select country_name
                from public.countries
                where culture = 'balkan'
            )
    ) as s)
, main_tbl_w_min_date as (
    select *, min("date"::date) over (partition by "tm_player_id", "joined_name") as player_first_join
    from main_tbl
)
select distinct on ("tm_player_id", "staff_id", "joined_name") * 
from main_tbl_w_min_date
where "date" <= player_first_join::date + interval '18 months'